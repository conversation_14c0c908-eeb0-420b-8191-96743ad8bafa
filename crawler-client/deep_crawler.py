#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度爬虫程序
使用 Playwright 实现列表页到详情页的完整爬取流程：
1. 爬取列表页，提取所有记录ID
2. 检查记录是否已爬取
3. 点击进入详情页
4. 爬取详情页信息
5. 记录已爬取状态
6. 返回列表页继续下一条

使用Playwright可视化点击分页方式，适用于大麦网
"""

import json
import os
import random
import re
import time
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Set
from urllib.parse import urljoin, urlparse

from bs4 import BeautifulSoup, Tag  # 添加BeautifulSoup导入
from playwright.async_api import Browser, Page, async_playwright

# 导入Playwright相关模块
from playwright.sync_api import Browser, Page, Playwright, sync_playwright
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError


class DeepCrawler:
    def __init__(self, base_url: str, output_dir: str = "crawl_data"):
        self.base_url = (
            base_url
            or "https://search.damai.cn/search.htm?ctl=%E8%AF%9D%E5%89%A7%E6%AD%8C%E5%89%A7&order=createtime_des"
        )
        self.output_dir = output_dir
        self.crawled_ids_file = os.path.join(output_dir, "crawled_ids.json")
        self.details_file = os.path.join(output_dir, "details_data.json")
        self.crawled_ids: Set[str] = set()
        self.details_data: List[Dict] = []

        # 添加停止标志
        self.should_stop = False

        # 添加日志收集
        self.logs: List[str] = []
        self.max_logs = 100  # 最多保留100条日志

        # Playwright相关属性
        self.playwright = None
        self.browser = None
        self.context = None  # 添加这一行
        self.page = None

        # 爬虫配置参数 - 添加默认值避免属性找不到错误
        self.delay_between_requests = 2.0
        self.max_items = 50
        self.max_pages = 10

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 加载已爬取的ID
        self._load_crawled_ids()
        self._load_details_data()

        self.log(f"🚀 深度爬虫初始化完成")
        self.log(f"📁 输出目录: {output_dir}")
        self.log(f"📊 已爬取记录: {len(self.crawled_ids)} 条")

        # 状态回调函数，用于更新client.py中的状态
        self.status_callback = None

        # 过滤条件
        self.filter_city = ""
        self.filter_category = ""
        self.filter_time_period = ""

    def log(self, message: str):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"

        # 添加到日志列表
        self.logs.append(log_entry)

        # 保持最新的日志数量
        if len(self.logs) > self.max_logs:
            self.logs = self.logs[-self.max_logs :]

        # 同时输出到控制台
        print(log_entry)

    def get_logs(self) -> List[str]:
        """获取日志列表"""
        return self.logs.copy()

    def clear_logs(self):
        """清空日志"""
        self.logs = []

    def stop(self):
        """设置停止标志"""
        self.should_stop = True
        self.log("🛑 收到停止信号，爬虫将在当前任务完成后停止")

    def is_stopped(self):
        """检查是否应该停止"""
        return self.should_stop

    def _load_crawled_ids(self):
        """加载已爬取的ID列表"""
        if os.path.exists(self.crawled_ids_file):
            try:
                with open(self.crawled_ids_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.crawled_ids = set(data.get("crawled_ids", []))
                self.log(f"✅ 加载已爬取ID: {len(self.crawled_ids)} 个")
            except Exception as e:
                self.log(f"⚠️  加载已爬取ID失败: {e}")

    def _save_crawled_ids(self):
        """保存已爬取的ID列表"""
        try:
            data = {
                "crawled_ids": list(self.crawled_ids),
                "last_update": datetime.now().isoformat(),
                "total_count": len(self.crawled_ids),
            }
            with open(self.crawled_ids_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"⚠️  保存已爬取ID失败: {e}")

    def _load_details_data(self):
        """加载详情数据"""
        if os.path.exists(self.details_file):
            try:
                with open(self.details_file, "r", encoding="utf-8") as f:
                    self.details_data = json.load(f)
                self.log(f"✅ 加载详情数据: {len(self.details_data)} 条")
            except Exception as e:
                self.log(f"⚠️  加载详情数据失败: {e}")

    def _save_details_data(self):
        """保存详情数据"""
        try:
            with open(self.details_file, "w", encoding="utf-8") as f:
                json.dump(self.details_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"⚠️  保存详情数据失败: {e}")

    def setup_playwright_browser(self, headless: bool = False):
        """设置Playwright Chrome浏览器 - 增强版异常处理"""
        try:
            self.log("🔧 正在启动Playwright浏览器...")

            # 1. 环境诊断
            self._diagnose_environment()

            # 2. 检查是否在 Docker 环境中
            in_docker = os.path.exists("/.dockerenv")
            if in_docker:
                self.log("🐳 检测到 Docker 环境，应用优化配置")
                headless = True  # Docker 中强制使用 headless 模式

                # 启动虚拟显示
                self._start_virtual_display()

            # 3. 启动 Playwright
            self.log("🚀 启动 Playwright...")
            self.playwright = sync_playwright().start()

            # 4. 配置启动选项 - 针对 CentOS 优化
            launch_options = self._get_optimized_launch_options(headless, in_docker)

            # 5. 启动浏览器
            self.log("🌐 正在创建浏览器实例...")
            self.log(f"📋 启动参数: {launch_options}")

            # 增加启动超时时间
            if in_docker:
                launch_options["timeout"] = 120000  # 2分钟超时

            self.browser = self.playwright.chromium.launch(**launch_options)

            # 6. 创建上下文
            context_options = self._get_optimized_context_options(in_docker)
            self.log(f"📄 创建浏览器上下文...")
            self.context = self.browser.new_context(**context_options)

            # 7. 创建页面
            self.page = self.context.new_page()

            # 8. 设置超时 - Docker 环境中使用更长的超时时间
            timeout = 120000 if in_docker else 30000
            self.page.set_default_timeout(timeout)
            self.page.set_default_navigation_timeout(timeout)

            self.log(f"✅ Playwright浏览器启动成功 (超时: {timeout}ms)")

            # 9. 测试浏览器功能
            self._test_browser_functionality()

            return True

        except Exception as e:
            self.log(f"❌ Playwright浏览器启动失败: {e}")
            self._log_detailed_error(e)
            self._suggest_solutions(e)
            return False

    def _diagnose_environment(self):
        """诊断运行环境"""
        self.log("🔍 环境诊断...")

        try:
            # 检查操作系统
            import platform

            os_info = platform.system() + " " + platform.release()
            self.log(f"   操作系统: {os_info}")

            # 检查架构
            arch = platform.machine()
            self.log(f"   架构: {arch}")

            # 检查是否在容器中
            in_docker = os.path.exists("/.dockerenv")
            self.log(f"   Docker 环境: {'是' if in_docker else '否'}")

            # 检查内存
            if os.path.exists("/proc/meminfo"):
                with open("/proc/meminfo", "r") as f:
                    for line in f:
                        if line.startswith("MemTotal:"):
                            mem_total = int(line.split()[1]) // 1024  # KB to MB
                            self.log(f"   总内存: {mem_total} MB")
                            break

            # 检查关键系统库
            critical_libs = [
                "/usr/lib/x86_64-linux-gnu/libnss3.so",
                "/usr/lib/x86_64-linux-gnu/libatk-1.0.so.0",
                "/usr/lib/x86_64-linux-gnu/libgtk-3.so.0",
                "/usr/lib/x86_64-linux-gnu/libgdk-3.so.0",
                "/usr/lib/x86_64-linux-gnu/libpango-1.0.so.0",
                "/usr/lib/x86_64-linux-gnu/libcairo2.so",
                "/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so.0",
                "/usr/lib/x86_64-linux-gnu/libatspi.so.0",
                "/usr/lib/x86_64-linux-gnu/libdrm.so.2",
                "/usr/lib/x86_64-linux-gnu/libxkbcommon.so.0",
                "/usr/lib/x86_64-linux-gnu/libxcomposite.so.1",
                "/usr/lib/x86_64-linux-gnu/libxdamage.so.1",
                "/usr/lib/x86_64-linux-gnu/libxrandr.so.2",
                "/usr/lib/x86_64-linux-gnu/libgbm.so.1",
                "/usr/lib/x86_64-linux-gnu/libasound.so.2",
            ]

            missing_libs = []
            for lib in critical_libs:
                if not os.path.exists(lib):
                    missing_libs.append(lib)

            if missing_libs:
                self.log(f"   ⚠️ 缺少系统库: {len(missing_libs)} 个")
                for lib in missing_libs[:3]:  # 只显示前3个
                    self.log(f"      - {lib}")
                if len(missing_libs) > 3:
                    self.log(f"      ... 还有 {len(missing_libs) - 3} 个")
            else:
                self.log("   ✅ 系统库检查通过")

        except Exception as e:
            self.log(f"   ❌ 环境诊断失败: {e}")

    def _start_virtual_display(self):
        """启动虚拟显示"""
        try:
            self.log("🖥️ 启动虚拟显示服务器...")

            # 检查 Xvfb 是否可用
            import subprocess

            result = subprocess.run(["which", "Xvfb"], capture_output=True, text=True)
            if result.returncode != 0:
                self.log("   ❌ Xvfb 不可用，尝试安装...")
                subprocess.run(["apt-get", "update"], capture_output=True)
                subprocess.run(
                    ["apt-get", "install", "-y", "xvfb"], capture_output=True
                )

            # 启动 Xvfb
            subprocess.Popen(
                ["Xvfb", ":99", "-screen", "0", "1200x800x24", "-ac"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
            )

            # 设置环境变量
            os.environ["DISPLAY"] = ":99"

            # 等待启动
            import time

            time.sleep(3)

            self.log("   ✅ 虚拟显示服务器启动成功")

        except Exception as e:
            self.log(f"   ❌ 虚拟显示启动失败: {e}")

    def _get_optimized_launch_options(self, headless: bool, in_docker: bool):
        """获取优化的启动选项"""
        base_args = [
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--window-size=1200,800",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-images",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-field-trial-config",
            "--disable-ipc-flooding-protection",
            "--memory-pressure-off",
            "--max_old_space_size=4096",
        ]

        # Docker 环境特殊配置
        if in_docker:
            base_args.extend(
                [
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--single-process",
                    "--disable-background-networking",
                    "--disable-default-apps",
                    "--disable-sync",
                    "--disable-translate",
                    "--hide-scrollbars",
                    "--mute-audio",
                    "--no-sandbox",
                    "--disable-gpu-sandbox",
                    "--disable-software-rasterizer",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--disable-hang-monitor",
                    "--disable-prompt-on-repost",
                    "--disable-client-side-phishing-detection",
                    "--disable-component-extensions-with-background-pages",
                    "--disable-domain-reliability",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-print-preview",
                    "--disable-sync",
                    "--disable-web-security",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--disable-hang-monitor",
                    "--disable-prompt-on-repost",
                    "--disable-client-side-phishing-detection",
                    "--disable-component-extensions-with-background-pages",
                    "--disable-domain-reliability",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-print-preview",
                    "--disable-sync",
                    "--disable-web-security",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--disable-hang-monitor",
                    "--disable-prompt-on-repost",
                    "--disable-client-side-phishing-detection",
                    "--disable-component-extensions-with-background-pages",
                    "--disable-domain-reliability",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-print-preview",
                    "--disable-sync",
                    "--disable-web-security",
                ]
            )

        return {
            "headless": headless,
            "args": base_args,
            "timeout": 120000 if in_docker else 60000,
        }

    def _get_optimized_context_options(self, in_docker: bool):
        """获取优化的上下文选项"""
        options = {
            "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "viewport": {"width": 1200, "height": 800},
            "ignore_https_errors": True,
            "bypass_csp": True,
        }

        if in_docker:
            options.update(
                {
                    "java_script_enabled": True,
                    "extra_http_headers": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                        "Accept-Encoding": "gzip, deflate, br",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1",
                    },
                }
            )

        return options

    def _test_browser_functionality(self):
        """测试浏览器功能"""
        try:
            self.log("🧪 测试浏览器功能...")

            # 测试访问简单页面
            self.page.goto(
                "https://httpbin.org/html", wait_until="domcontentloaded", timeout=30000
            )
            title = self.page.title()
            self.log(f"   ✅ 页面访问测试成功，标题: {title}")

            # 测试 JavaScript 执行
            result = self.page.evaluate("() => 'JavaScript 工作正常'")
            self.log(f"   ✅ JavaScript 测试成功: {result}")

            # 测试截图功能
            screenshot_data = self.page.screenshot()
            self.log(f"   ✅ 截图测试成功，大小: {len(screenshot_data)} bytes")

        except Exception as e:
            self.log(f"   ❌ 浏览器功能测试失败: {e}")

    def _log_detailed_error(self, error):
        """记录详细错误信息"""
        import traceback

        self.log("📋 详细错误信息:")
        self.log(f"   错误类型: {type(error).__name__}")
        self.log(f"   错误消息: {str(error)}")
        self.log(f"   堆栈跟踪:")
        for line in traceback.format_exc().split("\n"):
            if line.strip():
                self.log(f"     {line}")

    def _suggest_solutions(self, error):
        """根据错误类型提供解决方案建议"""
        error_str = str(error).lower()

        self.log("💡 解决方案建议:")

        if "timeout" in error_str:
            self.log("   1. 增加超时时间")
            self.log("   2. 检查网络连接")
            self.log("   3. 确保容器有足够内存")

        elif "permission" in error_str or "access" in error_str:
            self.log("   1. 使用 --privileged 运行容器")
            self.log("   2. 添加必要的 capabilities")
            self.log("   3. 检查 SELinux 设置")

        elif "library" in error_str or "dependency" in error_str:
            self.log("   1. 安装缺失的系统库")
            self.log("   2. 重新安装 Playwright 依赖")
            self.log("   3. 使用 --with-deps 安装浏览器")

        elif "display" in error_str or "xvfb" in error_str:
            self.log("   1. 启动虚拟显示服务器")
            self.log("   2. 设置 DISPLAY 环境变量")
            self.log("   3. 安装 X11 相关包")

        elif "memory" in error_str or "oom" in error_str:
            self.log("   1. 增加容器内存限制")
            self.log("   2. 减少并发数量")
            self.log("   3. 优化浏览器启动参数")

        else:
            self.log("   1. 检查 Docker 容器配置")
            self.log("   2. 查看系统日志")
            self.log("   3. 尝试重新构建镜像")

    def close_playwright_browser(self):
        """关闭Playwright浏览器"""
        try:
            if hasattr(self, "page") and self.page:
                self.page.close()
                self.page = None
            if hasattr(self, "context") and self.context:
                self.context.close()
                self.context = None
            if hasattr(self, "browser") and self.browser:
                self.browser.close()
                self.browser = None
            if hasattr(self, "playwright") and self.playwright:
                self.playwright.stop()
                self.playwright = None
            self.log("🔒 浏览器已关闭")
        except Exception as e:
            self.log(f"⚠️ 关闭浏览器时出错: {e}")

    def take_screenshot(self, filename: str):
        """截图"""
        try:
            screenshot_path = os.path.join(self.output_dir, filename)
            self.page.screenshot(path=screenshot_path)
            self.log(f"📸 截图已保存: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            self.log(f"⚠️ 截图失败: {e}")
            return None

    def extract_items_from_playwright_page(self) -> List[Dict[str, str]]:
        """从Playwright当前页面提取项目"""
        items = []

        try:
            # 等待页面加载 - 增加超时时间到30秒
            self.page.wait_for_load_state("domcontentloaded", timeout=30000)
            time.sleep(2)

            # 查找大麦网的项目链接
            item_links = self.page.locator('a[href*="detail.damai.cn/item.htm"]').all()

            self.log(f"🔍 在当前页面找到 {len(item_links)} 个项目")

            for link in item_links:
                try:
                    href = link.get_attribute("href")
                    if href and "detail.damai.cn/item.htm" in href:
                        # 提取ID
                        id_match = re.search(r"id=(\d+)", href)
                        if id_match:
                            item_id = id_match.group(1)
                            # 确保URL有正确的协议
                            if href.startswith("//"):
                                href = "https:" + href
                            elif not href.startswith(("http://", "https://")):
                                href = "https://" + href
                            items.append(
                                {"id": item_id, "url": href, "type": "damai_item"}
                            )
                except Exception as e:
                    continue

            # 去重
            unique_items = []
            seen_ids = set()
            for item in items:
                if item["id"] not in seen_ids:
                    unique_items.append(item)
                    seen_ids.add(item["id"])

            self.log(f"📋 提取到 {len(unique_items)} 个唯一项目")
            return unique_items

        except Exception as e:
            self.log(f"❌ 提取项目失败: {e}")
            return []

    def set_filters(self, city="", category="", time_period=""):
        """设置过滤条件"""
        self.filter_city = city
        self.filter_category = category
        self.filter_time_period = time_period
        self.log(f"🔧 设置过滤条件: 城市={city}, 分类={category}, 时间={time_period}")

    def set_status_callback(self, callback):
        """设置状态回调函数"""
        self.status_callback = callback

    def update_status(self, progress=None, total=None, current_item=None, message=None):
        """更新状态"""
        if self.status_callback:
            self.status_callback(
                progress=progress,
                total=total,
                current_item=current_item,
                message=message,
            )

    def crawl_multiple_pages_with_playwright(self, max_pages: int = 5):
        """使用Playwright爬取多个页面 - 增强版超时处理"""
        self.log(f"🚀 开始使用Playwright爬取多个页面，最大页数: {max_pages}")
        self.log(f"📋 将尝试爬取从第1页到第{max_pages}页的所有数据")

        # 确保 delay_between_requests 属性存在，避免属性找不到错误
        if not hasattr(self, "delay_between_requests"):
            self.delay_between_requests = 2.0
            self.log(
                f"⚠️  delay_between_requests 属性未初始化，使用默认值: {self.delay_between_requests}秒"
            )

        all_items = []

        # 用于记录实际检测到的最大页数
        detected_max_pages = max_pages
        page_num = 0  # 初始化，记录实际爬取的页数

        for page_num in range(1, max_pages + 1):
            # 检查是否应该停止
            if self.is_stopped():
                self.log("🛑 检测到停止信号，停止页面爬取")
                break

            # 检查是否超出实际最大页数
            if page_num > detected_max_pages:
                self.log(
                    f"📄 第 {page_num} 页超出实际最大页数 {detected_max_pages}，停止爬取"
                )
                break

            self.log(f"\n📄 === 第 {page_num} 页 ===")
            self.update_status(message=f"正在爬取第 {page_num} 页...")

            if page_num == 1:
                # 第一页：使用智能加载策略
                self.log(f"🌐 智能加载第一页: {self.base_url}")

                # 尝试多种加载策略
                page_loaded = self._smart_page_load(self.base_url, page_num)
                if not page_loaded:
                    self.log(f"❌ 第 {page_num} 页加载失败，跳过")
                    continue

                self.log(f"✅ 第一页加载完成，当前URL: {self.page.url}")

                # 应用过滤条件
                if self.filter_city or self.filter_category or self.filter_time_period:
                    self.log("🔧 检测到过滤条件，开始应用过滤...")
                    if not self._apply_page_filters():
                        self.log("⚠️ 过滤条件应用失败，继续使用当前页面")
                    else:
                        self.log("✅ 过滤条件应用成功")

                # 在第一页检测实际的分页总数
                detected_max_pages = self._detect_max_pages()
                if detected_max_pages < max_pages:
                    self.log(
                        f"🔍 检测到实际最大页数: {detected_max_pages}，调整爬取范围"
                    )
                    self.log(
                        f"📊 原计划爬取: {max_pages} 页，实际可爬取: {detected_max_pages} 页"
                    )
                else:
                    self.log(
                        f"🔍 检测到实际最大页数: {detected_max_pages}，继续按计划爬取"
                    )
            else:
                # 后续页面：尝试点击分页
                self.log(f"🔄 尝试跳转到第 {page_num} 页...")

                # 记录跳转前的URL和页面特征
                old_url = self.page.url
                old_content_hash = hash(
                    self.page.content()[:1000]
                )  # 使用页面前1000字符的hash作为特征

                # 多种分页元素选择器 - 优先使用Element UI分页
                pagination_selectors = [
                    # Element UI分页选择器 - 最优先
                    f'.el-pager li.number:has-text("{page_num}"):not(.active)',
                    f'ul.el-pager li.number:has-text("{page_num}"):not(.active)',
                    f'.el-pager .number:has-text("{page_num}"):not(.active)',
                    # Element UI下一页按钮（如果是连续翻页的话）
                    ".el-pagination__next:not(.is-disabled)" if page_num == 2 else None,
                    # 备选Element UI选择器
                    f'.el-pager li:has-text("{page_num}"):not(.active)',
                    f'.el-pagination li:has-text("{page_num}"):not(.active)',
                    # 直接页码链接
                    f'a:has-text("{page_num}")',
                    f'button:has-text("{page_num}")',
                    f'span:has-text("{page_num}"):not(.current)',
                    f'li:has-text("{page_num}") a',
                    # 下一页按钮 - 更全面的选择器
                    'button:has-text("下一页")',
                    'a:has-text("下一页")',
                    'span:has-text("下一页")',
                    'li:has-text("下一页") a',
                    'div:has-text("下一页")',
                    '[title*="下一页"]',
                    '[aria-label*="下一页"]',
                    # 英文分页
                    'button:has-text("Next")',
                    'a:has-text("Next")',
                    'span:has-text("Next")',
                    'li:has-text("Next") a',
                    'div:has-text("Next")',
                    '[title*="Next"]',
                    '[aria-label*="Next"]',
                    # 通用分页选择器
                    ".pagination a",
                    ".pagination button",
                    ".pager a",
                    ".pager button",
                    '[class*="pagination"] a',
                    '[class*="pager"] a',
                    '[class*="page"] a',
                    '[class*="page"] button',
                ]

                # 过滤掉None值
                pagination_selectors = [
                    s for s in pagination_selectors if s is not None
                ]

                page_clicked = False
                for selector in pagination_selectors:
                    try:
                        self.log(f"🔍 尝试点击分页元素: {selector}")

                        # 等待元素出现
                        element = self.page.wait_for_selector(selector, timeout=10000)
                        if element:
                            # 检查元素是否可见和可点击
                            if element.is_visible() and element.is_enabled():
                                self.log(f"✅ 找到可点击的分页元素: {selector}")

                                # 模拟真实用户行为：随机延迟
                                import random

                                delay = random.uniform(1.0, 3.0)
                                self.log(f"   ⏳ 模拟用户思考时间: {delay:.1f}秒")
                                time.sleep(delay)

                                # 改进的点击策略：先尝试普通点击，再尝试JavaScript点击
                                click_success = False

                                # 策略1: 普通点击
                                try:
                                    element.click()
                                    click_success = True
                                    self.log(f"   ✅ 普通点击成功")
                                except Exception as e:
                                    self.log(f"   ⚠️ 普通点击失败: {e}")

                                # 策略2: JavaScript点击（如果普通点击失败）
                                if not click_success:
                                    try:
                                        self.page.evaluate(f"""
                                            const element = document.querySelector("{selector}");
                                            if (element) {{
                                                element.click();
                                            }}
                                        """)
                                        click_success = True
                                        self.log(f"   ✅ JavaScript点击成功")
                                    except Exception as e:
                                        self.log(f"   ⚠️ JavaScript点击失败: {e}")

                                if not click_success:
                                    self.log(f"   ❌ 所有点击策略都失败")
                                    continue

                                # 改进的等待策略：分阶段等待和检查
                                self.log(f"   ⏳ 等待页面响应...")

                                # 阶段1: 短暂等待，让页面开始响应
                                time.sleep(2)

                                # 阶段2: 等待网络活动
                                try:
                                    self.page.wait_for_load_state(
                                        "networkidle", timeout=20000
                                    )
                                    self.log(f"   ✅ 网络活动完成")
                                except Exception as e:
                                    self.log(f"   ⚠️ 网络等待超时: {e}")

                                # 阶段3: 额外等待确保页面稳定
                                time.sleep(3)

                                # 改进的页面变化检测
                                new_url = self.page.url
                                new_content = self.page.content()
                                new_content_hash = hash(
                                    new_content[:2000]
                                )  # 增加检测长度

                                # 多重检测条件
                                url_changed = new_url != old_url
                                content_changed = new_content_hash != old_content_hash

                                # 检测页面中的页码是否变化
                                page_number_changed = False
                                try:
                                    # 检查当前活跃的页码
                                    active_page_selectors = [
                                        ".el-pager li.number.active",
                                        ".el-pager .number.active",
                                        ".pagination .active",
                                        ".current",
                                    ]

                                    for active_selector in active_page_selectors:
                                        try:
                                            active_element = self.page.query_selector(
                                                active_selector
                                            )
                                            if active_element:
                                                active_text = (
                                                    active_element.inner_text().strip()
                                                )
                                                if active_text == str(page_num):
                                                    page_number_changed = True
                                                    self.log(
                                                        f"   ✅ 检测到活跃页码变为: {active_text}"
                                                    )
                                                    break
                                        except:
                                            continue
                                except Exception as e:
                                    self.log(f"   ⚠️ 页码检测失败: {e}")

                                # 综合判断页面是否真的发生了变化
                                if (
                                    url_changed
                                    or content_changed
                                    or page_number_changed
                                ):
                                    change_reasons = []
                                    if url_changed:
                                        change_reasons.append(
                                            f"URL变化: {old_url} -> {new_url}"
                                        )
                                    if content_changed:
                                        change_reasons.append("内容变化")
                                    if page_number_changed:
                                        change_reasons.append("页码变化")

                                    self.log(
                                        f"✅ 页面跳转成功，变化原因: {'; '.join(change_reasons)}"
                                    )
                                    page_clicked = True
                                    break
                                else:
                                    self.log(f"⚠️ 页面未发生变化，尝试下一个选择器")
                                    # 截图调试
                                    self.take_screenshot(
                                        f"no_change_page_{page_num}.png"
                                    )
                            else:
                                self.log(f"⚠️ 元素不可见或不可点击: {selector}")
                        else:
                            self.log(f"⚠️ 未找到元素: {selector}")

                    except Exception as e:
                        self.log(f"⚠️ 点击分页元素失败 {selector}: {e}")
                        continue

                if not page_clicked:
                    # 备选策略：尝试通过URL参数直接跳转
                    self.log(f"🔄 尝试备选策略：URL参数跳转到第 {page_num} 页")
                    try:
                        # 构建带页码参数的URL
                        current_url = self.page.url
                        if "?" in current_url:
                            # 已有参数，添加或更新页码参数
                            if "page=" in current_url:
                                # 更新现有page参数
                                import re

                                new_url = re.sub(
                                    r"page=\d+", f"page={page_num}", current_url
                                )
                            else:
                                # 添加page参数
                                new_url = f"{current_url}&page={page_num}"
                        else:
                            # 没有参数，直接添加
                            new_url = f"{current_url}?page={page_num}"

                        self.log(f"   🌐 尝试访问: {new_url}")

                        # 保存当前页面内容用于比较
                        old_backup_content = self.page.content()[:2000]

                        # 访问新URL
                        self.page.goto(new_url, wait_until="networkidle", timeout=30000)
                        time.sleep(3)

                        # 检查是否成功跳转
                        new_backup_content = self.page.content()[:2000]
                        if hash(new_backup_content) != hash(old_backup_content):
                            self.log(f"   ✅ URL跳转成功")
                            page_clicked = True
                        else:
                            self.log(f"   ⚠️ URL跳转后页面无变化")
                    except Exception as e:
                        self.log(f"   ❌ URL跳转失败: {e}")

                if not page_clicked:
                    self.log(
                        f"❌ 所有跳转策略都失败，无法跳转到第 {page_num} 页，停止爬取"
                    )
                    break

            # 提取当前页面的项目
            self.log(f"🔍 提取第 {page_num} 页的项目...")
            page_items = self.extract_items_from_playwright_page()

            if page_items:
                self.log(f"📋 第 {page_num} 页提取到 {len(page_items)} 个项目")
                all_items.extend(page_items)

                # 去重
                unique_items = []
                seen_ids = set()
                for item in all_items:
                    if item["id"] not in seen_ids:
                        unique_items.append(item)
                        seen_ids.add(item["id"])

                all_items = unique_items
                self.log(f"📊 累计唯一项目: {len(all_items)} 个")
            else:
                self.log(f"⚠️ 第 {page_num} 页未提取到项目")

            # 截图保存
            self.take_screenshot(f"page_{page_num}.png")

            # 延迟
            if page_num < max_pages:
                self.log(f"⏳ 等待 {self.delay_between_requests} 秒后继续...")
                time.sleep(self.delay_between_requests)

        # 统计实际爬取情况
        actual_pages = min(page_num, detected_max_pages)
        self.log(f"🎉 页面爬取完成！")
        self.log(f"📊 爬取统计:")
        self.log(f"   - 计划爬取: {max_pages} 页")
        self.log(f"   - 检测最大: {detected_max_pages} 页")
        self.log(f"   - 实际爬取: {actual_pages} 页")
        self.log(f"   - 提取项目: {len(all_items)} 个唯一项目")

        # 如果实际页数小于计划页数，说明智能检测生效了
        if detected_max_pages < max_pages:
            self.log(
                f"✅ 智能检测生效：避免了 {max_pages - detected_max_pages} 个无效页面的爬取"
            )
        return all_items

    def _smart_page_load(self, url: str, page_num: int) -> bool:
        """智能页面加载策略"""
        self.log(f"🧠 使用智能加载策略访问: {url}")

        # 策略1: 标准加载
        try:
            self.log("📋 策略1: 标准加载 (networkidle)")
            self.page.goto(url, wait_until="networkidle", timeout=30000)
            self.log("✅ 策略1成功")
            return True
        except Exception as e:
            self.log(f"❌ 策略1失败: {e}")

        # 策略2: DOM加载完成
        try:
            self.log("📋 策略2: DOM加载完成")
            self.page.goto(url, wait_until="domcontentloaded", timeout=30000)
            # 等待一段时间让页面稳定
            time.sleep(5)
            self.log("✅ 策略2成功")
            return True
        except Exception as e:
            self.log(f"❌ 策略2失败: {e}")

        # 策略3: 最小等待
        try:
            self.log("📋 策略3: 最小等待")
            self.page.goto(url, wait_until="commit", timeout=30000)
            # 等待页面基本加载
            time.sleep(10)
            self.log("✅ 策略3成功")
            return True
        except Exception as e:
            self.log(f"❌ 策略3失败: {e}")

        # 策略4: 分步加载
        try:
            self.log("📋 策略4: 分步加载")
            self.page.goto(url, timeout=30000)
            # 等待页面开始加载
            time.sleep(3)
            # 等待网络空闲
            self.page.wait_for_load_state("networkidle", timeout=30000)
            self.log("✅ 策略4成功")
            return True
        except Exception as e:
            self.log(f"❌ 策略4失败: {e}")

        # 策略5: 最后尝试 - 不等待任何状态
        try:
            self.log("📋 策略5: 不等待状态")
            self.page.goto(url, timeout=30000)
            # 强制等待
            time.sleep(15)
            self.log("✅ 策略5成功")
            return True
        except Exception as e:
            self.log(f"❌ 策略5失败: {e}")

        self.log("❌ 所有加载策略都失败了")
        return False

    def _detect_max_pages(self) -> int:
        """检测分页的实际最大页数"""
        try:
            self.log("🔍 开始检测分页最大页数...")

            # 多种分页总数检测策略
            max_page_selectors = [
                # Element UI 分页最后一页
                ".el-pager li.number:last-of-type",
                ".el-pager .number:last-of-type",
                # 通用分页最后一页
                ".pagination li:last-child a",
                ".pagination a:last-child",
                ".pager li:last-child a",
                ".pager a:last-child",
                # 可能包含页数信息的元素
                ".pagination-info",
                ".page-info",
                ".total-pages",
                # 页数范围显示 (如 "1/10")
                '[class*="page"]',
                '[class*="pagination"]',
            ]

            detected_pages = []

            # 策略1: 查找最后一页的页码
            for selector in max_page_selectors:
                try:
                    elements = self.page.query_selector_all(selector)
                    if elements:
                        for element in elements:
                            text = element.inner_text().strip()
                            # 提取数字
                            import re

                            numbers = re.findall(r"\d+", text)
                            for num_str in numbers:
                                num = int(num_str)
                                if 1 <= num <= 100:  # 合理的页数范围
                                    detected_pages.append(num)
                                    self.log(f"   📍 从 {selector} 检测到页数: {num}")
                except Exception as e:
                    continue

            # 策略2: 查找所有页码按钮
            try:
                page_number_elements = self.page.query_selector_all(
                    ".el-pager li.number, .pagination li a, .pager li a"
                )
                for element in page_number_elements:
                    try:
                        text = element.inner_text().strip()
                        if text.isdigit():
                            num = int(text)
                            if 1 <= num <= 100:
                                detected_pages.append(num)
                                self.log(f"   📍 从页码按钮检测到: {num}")
                    except:
                        continue
            except Exception as e:
                self.log(f"   ⚠️ 页码按钮检测失败: {e}")

            # 策略3: 查找页面信息文本 (如 "共10页")
            try:
                page_content = self.page.content()
                import re

                # 匹配各种页数表达方式
                page_patterns = [
                    r"共\s*(\d+)\s*页",
                    r"总共\s*(\d+)\s*页",
                    r"共\s*(\d+)\s*条",
                    r"total\s*(\d+)\s*pages?",
                    r"(\d+)\s*页\s*$",
                    r"/\s*(\d+)\s*页",
                    r"第\s*\d+\s*/\s*(\d+)\s*页",
                ]

                for pattern in page_patterns:
                    matches = re.findall(pattern, page_content, re.IGNORECASE)
                    for match in matches:
                        num = int(match)
                        if 1 <= num <= 100:
                            detected_pages.append(num)
                            self.log(f"   📍 从页面文本检测到: {num} (模式: {pattern})")
            except Exception as e:
                self.log(f"   ⚠️ 页面文本检测失败: {e}")

            # 分析检测结果
            if detected_pages:
                # 去重并排序
                unique_pages = sorted(list(set(detected_pages)))
                self.log(f"   📊 检测到的所有页数: {unique_pages}")

                # 选择最大值作为总页数，但要合理性检查
                max_detected = max(unique_pages)

                # 如果检测到的最大页数过大，取较小的合理值
                if max_detected > 50:
                    # 查看是否有其他合理的值
                    reasonable_pages = [p for p in unique_pages if p <= 20]
                    if reasonable_pages:
                        max_detected = max(reasonable_pages)
                        self.log(f"   🔧 调整为更合理的页数: {max_detected}")

                self.log(f"✅ 最终检测到最大页数: {max_detected}")
                return max_detected
            else:
                self.log("⚠️ 未能检测到分页信息，使用默认值")
                return 10  # 默认值

        except Exception as e:
            self.log(f"❌ 检测分页最大页数失败: {e}")
            return 10  # 失败时使用默认值

    def _apply_page_filters(self) -> bool:
        """在大麦网列表页应用过滤条件"""
        try:
            self.log("🔧 开始应用页面过滤条件...")
            success = True

            # 应用城市过滤
            if self.filter_city:
                self.log(f"🏙️ 应用城市过滤: {self.filter_city}")
                if not self._apply_city_filter(self.filter_city):
                    self.log(f"⚠️ 城市过滤应用失败: {self.filter_city}")
                    success = False
                else:
                    # 等待页面更新
                    time.sleep(2)

            # 应用分类过滤
            if self.filter_category:
                self.log(f"📂 应用分类过滤: {self.filter_category}")
                if not self._apply_category_filter(self.filter_category):
                    self.log(f"⚠️ 分类过滤应用失败: {self.filter_category}")
                    success = False
                else:
                    # 等待页面更新
                    time.sleep(2)

            # 应用时间过滤
            if self.filter_time_period:
                self.log(f"⏰ 应用时间过滤: {self.filter_time_period}")
                if not self._apply_time_filter(self.filter_time_period):
                    self.log(f"⚠️ 时间过滤应用失败: {self.filter_time_period}")
                    success = False
                else:
                    # 等待页面更新
                    time.sleep(2)

            if success:
                self.log("✅ 所有过滤条件应用成功")
                # 等待页面最终更新完成
                time.sleep(3)
                self.page.wait_for_load_state("networkidle", timeout=10000)

            return success

        except Exception as e:
            self.log(f"❌ 应用页面过滤条件失败: {e}")
            return False

    def _apply_city_filter(self, city: str) -> bool:
        """应用城市过滤 - 基于Vue.js组件的新实现"""
        try:
            self.log(f"🏙️ 开始应用城市过滤: {city}")

            # 记录当前页面状态用于对比
            original_content_hash = hash(self.page.content()[:5000])

            # 获取当前选中的城市，用于对比变化
            original_selected_city = self._get_current_selected_city()
            self.log(f"📍 当前选中城市: {original_selected_city}")

            # 根据HTML结构，寻找城市过滤组件
            city_factor_selector = '.factor-item:has(.factor-title:has-text("城 市"))'

            # 首先检查城市过滤组件是否存在
            city_factor_element = self.page.query_selector(city_factor_selector)
            if not city_factor_element:
                self.log("❌ 未找到城市过滤组件")
                return False

            self.log("✅ 找到城市过滤组件")

            # 查找目标城市的span元素
            # 基于HTML结构：span.factor-content-item:has-text("城市名")
            city_selectors = [
                # 精确匹配城市过滤组件内的城市选项
                f'.factor-item .factor-content-item:has-text("{city}")',
                f'.factor-content .factor-content-item:has-text("{city}")',
                f'span.factor-content-item:has-text("{city}")',
                # 备选方案
                f'.factor-content-main span:has-text("{city}")',
                f'[class*="factor-content"] span:has-text("{city}")',
            ]

            city_element = None
            found_selector = None

            for selector in city_selectors:
                try:
                    elements = self.page.query_selector_all(selector)
                    if elements:
                        # 查找精确匹配的城市
                        for element in elements:
                            if (
                                element.is_visible()
                                and element.inner_text().strip() == city
                            ):
                                city_element = element
                                found_selector = selector
                                break
                        if city_element:
                            break
                except Exception as e:
                    self.log(f"⚠️ 城市选择器查找失败 {selector}: {e}")
                    continue

            if not city_element:
                self.log(f"❌ 未找到城市选项: {city}")

                # 调试信息：列出所有可用的城市选项
                self._debug_available_cities_vue()

                return False

            self.log(f"✅ 找到城市选项: {city} (使用选择器: {found_selector})")

            # 检查该城市是否已经是当前选中状态
            city_classes = city_element.get_attribute("class") or ""
            is_already_active = "factor-content-item-active" in city_classes

            if is_already_active:
                self.log(f"ℹ️ 城市 {city} 已经是选中状态，无需点击")
                return True

            # 点击城市选项
            self.log(f"🖱️ 点击城市选项: {city}")
            try:
                # 使用多种点击策略确保成功
                try:
                    city_element.click()
                    self.log("✅ 普通点击成功")
                except Exception as e:
                    self.log(f"⚠️ 普通点击失败，尝试JavaScript点击: {e}")
                    # 使用JavaScript点击作为备选
                    self.page.evaluate(f"""
                        const elements = document.querySelectorAll('.factor-content-item');
                        for (const el of elements) {{
                            if (el.textContent.trim() === "{city}") {{
                                el.click();
                                break;
                            }}
                        }}
                    """)
                    self.log("✅ JavaScript点击成功")

                # 等待页面响应
                self.log("⏳ 等待城市过滤生效...")
                time.sleep(2)  # Vue.js需要时间更新DOM

                # 等待可能的异步请求完成
                try:
                    self.page.wait_for_load_state("networkidle", timeout=8000)
                    self.log("✅ 网络请求完成")
                except:
                    self.log("⚠️ 网络等待超时，继续验证")

                # 验证城市过滤是否生效
                success = self._verify_city_filter_success(
                    city, original_content_hash, original_selected_city
                )

                if success:
                    self.log(f"🎉 城市过滤成功应用: {city}")
                else:
                    self.log(f"⚠️ 城市过滤可能未完全生效，但已点击: {city}")

                return success

            except Exception as e:
                self.log(f"❌ 点击城市选项失败: {e}")
                return False

        except Exception as e:
            self.log(f"❌ 城市过滤异常: {e}")
            import traceback

            self.log(f"❌ 异常详情: {traceback.format_exc()}")
            return False

    def _debug_available_cities(self):
        """调试函数：列出页面上所有可能的城市选项"""
        try:
            self.log("🔍 开始调试页面上的城市选项...")

            # 查找各种可能的城市容器
            city_containers = [
                ".city-list",
                ".city-selector",
                ".city-dropdown",
                ".search-filter-bar",
                ".filter-item",
                '[class*="city"]',
                '[class*="region"]',
            ]

            found_cities = set()

            for container_selector in city_containers:
                try:
                    containers = self.page.query_selector_all(container_selector)
                    if containers:
                        self.log(f"📋 在容器 {container_selector} 中查找城市选项...")

                        for container in containers:
                            if container.is_visible():
                                # 查找链接和按钮
                                links = container.query_selector_all(
                                    "a, button, span, div"
                                )
                                for link in links:
                                    try:
                                        text = link.inner_text().strip()
                                        href = link.get_attribute("href") or ""

                                        # 识别可能的城市名称
                                        if (
                                            text
                                            and len(text) <= 10
                                            and any(
                                                char in text
                                                for char in "北京上海广州深圳杭州南京苏州武汉成都重庆天津西安"
                                            )
                                        ):
                                            if text not in found_cities:
                                                found_cities.add(text)
                                                self.log(
                                                    f"   🏙️ 发现城市选项: '{text}' (href: {href[:50]}...)"
                                                )

                                        # 检查href中的城市参数
                                        if "cty=" in href or "city=" in href:
                                            import re

                                            city_matches = re.findall(
                                                r"(?:cty|city)=([^&]+)", href
                                            )
                                            for city_match in city_matches:
                                                try:
                                                    from urllib.parse import unquote

                                                    decoded_city = unquote(city_match)
                                                    if decoded_city not in found_cities:
                                                        found_cities.add(decoded_city)
                                                        self.log(
                                                            f"   🔗 从URL参数发现城市: '{decoded_city}'"
                                                        )
                                                except:
                                                    pass
                                    except:
                                        continue
                except Exception as e:
                    continue

            # 查找页面内容中的城市信息
            try:
                page_content = self.page.content()

                # 查找常见城市名称
                common_cities = [
                    "北京",
                    "上海",
                    "广州",
                    "深圳",
                    "杭州",
                    "南京",
                    "苏州",
                    "武汉",
                    "成都",
                    "重庆",
                    "天津",
                    "西安",
                    "长沙",
                    "厦门",
                    "青岛",
                ]
                for city in common_cities:
                    if city in page_content and city not in found_cities:
                        # 检查是否在可点击的元素中
                        city_elements = self.page.query_selector_all(
                            f'a:has-text("{city}"), button:has-text("{city}"), [data-city="{city}"]'
                        )
                        if city_elements:
                            found_cities.add(city)
                            self.log(f"   📍 在页面内容中发现城市: '{city}'")
            except Exception as e:
                self.log(f"   ⚠️ 搜索页面内容时出错: {e}")

            # 总结调试结果
            if found_cities:
                self.log(f"🎯 调试完成，共发现 {len(found_cities)} 个可能的城市选项:")
                for i, city in enumerate(sorted(found_cities), 1):
                    self.log(f"   {i}. {city}")
            else:
                self.log("❌ 调试完成，未发现任何城市选项")

                # 进一步调试：列出所有可能的过滤相关元素
                self.log("🔍 扩展调试：查找所有可能的过滤元素...")
                filter_elements = self.page.query_selector_all(
                    '[class*="filter"], [class*="search"], [class*="select"]'
                )
                for i, element in enumerate(filter_elements[:10]):  # 只显示前10个
                    try:
                        if element.is_visible():
                            element_text = element.inner_text().strip()[:50]
                            element_class = element.get_attribute("class") or ""
                            self.log(
                                f"   🔧 过滤元素 {i+1}: class='{element_class}', text='{element_text}'"
                            )
                    except:
                        continue

        except Exception as e:
            self.log(f"❌ 调试城市选项时出错: {e}")

    def _get_current_selected_city(self) -> str:
        """获取当前选中的城市"""
        try:
            # 根据HTML结构查找当前选中的城市
            selected_city_selectors = [
                ".factor-selected-city",  # HTML中显示的选中城市
                ".factor-content-item-active",  # 激活状态的城市选项
                ".factor-item .factor-selected .factor-selected-city",
            ]

            for selector in selected_city_selectors:
                try:
                    element = self.page.query_selector(selector)
                    if element and element.is_visible():
                        city_text = element.inner_text().strip()
                        if city_text:
                            return city_text
                except:
                    continue

            return "未知"
        except Exception as e:
            self.log(f"⚠️ 获取当前选中城市失败: {e}")
            return "未知"

    def _debug_available_cities_vue(self):
        """调试函数：基于Vue.js结构列出可用城市选项"""
        try:
            self.log("🔍 开始调试Vue.js城市组件...")

            # 查找城市过滤组件
            factor_item = self.page.query_selector(
                '.factor-item:has(.factor-title:has-text("城 市"))'
            )
            if not factor_item:
                self.log("❌ 未找到城市过滤组件 (.factor-item)")
                return

            self.log("✅ 找到城市过滤组件")

            # 查找所有城市选项
            city_elements = factor_item.query_selector_all(".factor-content-item")

            if not city_elements:
                self.log("❌ 未找到任何城市选项 (.factor-content-item)")
                return

            self.log(f"📋 找到 {len(city_elements)} 个城市选项:")

            found_cities = []
            for i, element in enumerate(city_elements):
                try:
                    if element.is_visible():
                        city_text = element.inner_text().strip()
                        city_classes = element.get_attribute("class") or ""
                        is_active = "factor-content-item-active" in city_classes
                        is_default = "factor-content-item-default" in city_classes

                        status_info = []
                        if is_active:
                            status_info.append("当前选中")
                        if is_default:
                            status_info.append("默认选项")

                        status_str = (
                            f" ({', '.join(status_info)})" if status_info else ""
                        )

                        self.log(f"   {i+1}. '{city_text}'{status_str}")
                        found_cities.append(
                            {
                                "text": city_text,
                                "active": is_active,
                                "default": is_default,
                                "classes": city_classes,
                            }
                        )
                except Exception as e:
                    self.log(f"   ❌ 解析第{i+1}个城市选项失败: {e}")
                    continue

            # 显示当前选中的城市
            current_selected = self._get_current_selected_city()
            self.log(f"🎯 当前显示的选中城市: '{current_selected}'")

            # 检查是否有"更多"按钮
            more_button = factor_item.query_selector(".factor-more")
            if more_button and more_button.is_visible():
                self.log("📋 发现'更多'按钮，可能还有隐藏的城市选项")

            return found_cities

        except Exception as e:
            self.log(f"❌ 调试Vue.js城市组件失败: {e}")
            return []

    def _verify_city_filter_success(
        self, target_city: str, original_content_hash: int, original_selected_city: str
    ) -> bool:
        """验证城市过滤是否成功应用"""
        try:
            self.log("🔍 开始验证城市过滤结果...")

            success_indicators = []

            # 验证1: 检查当前选中的城市是否已更改
            current_selected_city = self._get_current_selected_city()
            if current_selected_city == target_city:
                success_indicators.append("选中城市已更新")
                self.log(
                    f"✅ 选中城市已更新: {original_selected_city} -> {current_selected_city}"
                )
            else:
                self.log(
                    f"⚠️ 选中城市未更新: 期望={target_city}, 实际={current_selected_city}"
                )

            # 验证2: 检查激活状态的城市选项
            active_city_element = self.page.query_selector(
                ".factor-content-item-active"
            )
            if active_city_element:
                active_city_text = active_city_element.inner_text().strip()
                if active_city_text == target_city:
                    success_indicators.append("激活状态正确")
                    self.log(f"✅ 激活状态正确: {active_city_text}")
                else:
                    self.log(
                        f"⚠️ 激活状态不匹配: 期望={target_city}, 实际={active_city_text}"
                    )

            # 验证3: 检查页面内容是否发生变化（列表数据更新）
            new_content_hash = hash(self.page.content()[:5000])
            if new_content_hash != original_content_hash:
                success_indicators.append("页面内容已更新")
                self.log("✅ 页面内容已更新（可能是列表数据）")
            else:
                self.log("⚠️ 页面内容未发生变化")

            # 验证4: 检查是否有新的搜索结果加载
            try:
                # 等待一下，确保异步内容加载完成
                time.sleep(1)

                # 查找搜索结果容器
                result_containers = [
                    ".search-result",
                    ".item-list",
                    '[class*="result"]',
                    '[class*="list"]',
                ]

                for container_selector in result_containers:
                    container = self.page.query_selector(container_selector)
                    if container and container.is_visible():
                        # 检查是否有结果项
                        items = container.query_selector_all(
                            'a[href*="detail.damai.cn"]'
                        )
                        if items:
                            success_indicators.append(f"发现{len(items)}个搜索结果")
                            self.log(f"✅ 发现 {len(items)} 个搜索结果")
                            break

            except Exception as e:
                self.log(f"⚠️ 检查搜索结果时出错: {e}")

            # 综合判断
            if success_indicators:
                self.log(
                    f"🎉 城市过滤验证成功! 成功指标: {', '.join(success_indicators)}"
                )
                return True
            else:
                self.log("⚠️ 城市过滤验证失败，未找到成功指标")
                return False

        except Exception as e:
            self.log(f"❌ 验证城市过滤时出错: {e}")
            return False

    def test_city_filter_vue(self, city: str) -> bool:
        """测试Vue.js城市过滤功能（独立测试方法）"""
        try:
            self.log(f"🧪 开始测试城市过滤功能: {city}")

            # 确保浏览器已启动
            if not self.page:
                self.log("❌ 浏览器未启动，无法测试")
                return False

            # 访问测试页面（如果需要）
            current_url = self.page.url
            if not current_url or "damai.cn" not in current_url:
                test_url = "https://search.damai.cn/search.htm?ctl=%E8%AF%9D%E5%89%A7%E6%AD%8C%E5%89%A7&order=createtime_des"
                self.log(f"🌐 导航到测试页面: {test_url}")
                self.page.goto(test_url, wait_until="networkidle", timeout=30000)
                time.sleep(3)

            # 调试当前可用的城市选项
            self.log("🔍 调试当前页面的城市选项...")
            available_cities = self._debug_available_cities_vue()

            # 检查目标城市是否可用
            target_city_available = any(
                city_info["text"] == city for city_info in available_cities
            )
            if not target_city_available:
                self.log(f"⚠️ 目标城市 '{city}' 在当前页面不可用")
                available_city_names = [
                    city_info["text"] for city_info in available_cities
                ]
                self.log(f"📋 可用城市: {available_city_names}")

                # 如果有可用城市，使用第一个非默认城市进行测试
                test_cities = [
                    c["text"]
                    for c in available_cities
                    if not c["default"] and c["text"] != "全部"
                ]
                if test_cities:
                    test_city = test_cities[0]
                    self.log(f"🔄 改用可用城市进行测试: {test_city}")
                    city = test_city
                else:
                    self.log("❌ 没有可用的测试城市")
                    return False

            # 执行城市过滤测试
            result = self._apply_city_filter(city)

            self.log(f"🧪 城市过滤测试结果: {'成功' if result else '失败'}")
            return result

        except Exception as e:
            self.log(f"❌ 测试城市过滤功能时出错: {e}")
            import traceback

            self.log(f"❌ 错误详情: {traceback.format_exc()}")
            return False

    def _apply_category_filter(self, category: str) -> bool:
        """应用分类过滤"""
        try:
            # 大麦网分类选择器
            category_selectors = [
                f'a[href*="ctl={category}"]',
                f'a:has-text("{category}")',
                f'span:has-text("{category}")',
                f'div:has-text("{category}")',
                f'li:has-text("{category}")',
                # 分类标签
                f'.category-list a:has-text("{category}")',
                f'.category-selector a:has-text("{category}")',
                f'[data-category="{category}"]',
                # 导航菜单中的分类
                f'.nav-category a:has-text("{category}")',
                f'.main-nav a:has-text("{category}")',
            ]

            # 首先尝试打开分类选择器
            category_dropdown_selectors = [
                ".category-selector",
                ".category-dropdown",
                ".nav-category",
                '[class*="category"]',
                '[class*="nav"]',
            ]

            for dropdown_selector in category_dropdown_selectors:
                try:
                    dropdown = self.page.query_selector(dropdown_selector)
                    if dropdown and dropdown.is_visible():
                        self.log(f"🔍 找到分类选择器: {dropdown_selector}")
                        dropdown.click()
                        time.sleep(1)
                        break
                except:
                    continue

            # 尝试点击分类选项
            for selector in category_selectors:
                try:
                    self.log(f"🔍 尝试分类选择器: {selector}")
                    element = self.page.wait_for_selector(selector, timeout=5000)
                    if element and element.is_visible() and element.is_enabled():
                        self.log(f"✅ 找到分类选项: {category}")
                        element.click()
                        return True
                except:
                    continue

            self.log(f"❌ 未找到分类选项: {category}")
            return False

        except Exception as e:
            self.log(f"❌ 分类过滤失败: {e}")
            return False

    def _apply_time_filter(self, time_period: str) -> bool:
        """应用时间过滤"""
        try:
            # 大麦网时间选择器
            time_selectors = [
                f'a:has-text("{time_period}")',
                f'span:has-text("{time_period}")',
                f'div:has-text("{time_period}")',
                f'li:has-text("{time_period}")',
                f'button:has-text("{time_period}")',
                # 时间标签
                f'.time-list a:has-text("{time_period}")',
                f'.time-selector a:has-text("{time_period}")',
                f'[data-time="{time_period}"]',
                # 日期选择器
                f'.date-selector a:has-text("{time_period}")',
                f'.time-filter a:has-text("{time_period}")',
            ]

            # 首先尝试打开时间选择器
            time_dropdown_selectors = [
                ".time-selector",
                ".time-dropdown",
                ".date-selector",
                '[class*="time"]',
                '[class*="date"]',
            ]

            for dropdown_selector in time_dropdown_selectors:
                try:
                    dropdown = self.page.query_selector(dropdown_selector)
                    if dropdown and dropdown.is_visible():
                        self.log(f"🔍 找到时间选择器: {dropdown_selector}")
                        dropdown.click()
                        time.sleep(1)
                        break
                except:
                    continue

            # 尝试点击时间选项
            for selector in time_selectors:
                try:
                    self.log(f"🔍 尝试时间选择器: {selector}")
                    element = self.page.wait_for_selector(selector, timeout=5000)
                    if element and element.is_visible() and element.is_enabled():
                        self.log(f"✅ 找到时间选项: {time_period}")
                        element.click()
                        return True
                except:
                    continue

            self.log(f"❌ 未找到时间选项: {time_period}")
            return False

        except Exception as e:
            self.log(f"❌ 时间过滤失败: {e}")
            return False

    def extract_performance_schedule(self, content: str) -> Dict:
        """提取演出日程信息 - 包含完整的城市、日期、场次、票档关联关系"""
        performance_data = {
            "cities": [],  # 城市列表
            "dates": [],  # 日期列表
            "sessions": [],  # 场次列表
            "ticket_tiers": [],  # 票档列表
            "schedule_matrix": {},  # 城市-日期-场次-票档关联矩阵
            "has_city_selection": False,  # 是否有城市选择
            "has_date_selection": False,  # 是否有日期选择
        }

        try:
            # 使用最佳的场次提取方法
            print("🎭 使用perform__order__select类提取场次")
            sessions_from_perform_order = self.extract_sessions_by_perform_order_class(
                content
            )
            if sessions_from_perform_order:
                print(f"✅ 提取到 {len(sessions_from_perform_order)} 个场次")
                performance_data["sessions"] = sessions_from_perform_order
                performance_data["dates"] = list(
                    set(
                        [
                            s["date"]
                            for s in sessions_from_perform_order
                            if s.get("date")
                        ]
                    )
                )
                performance_data["has_date_selection"] = (
                    len(performance_data["dates"]) > 1
                )
            else:
                print("⚠️ 未找到场次信息")

            # 使用最佳的票档提取方法
            print("🎫 使用perform__order__select类提取票档")
            tickets_from_perform_order = self.extract_tickets_by_perform_order_class(
                content
            )
            if tickets_from_perform_order:
                print(f"✅ 提取到 {len(tickets_from_perform_order)} 个票档")
                performance_data["ticket_tiers"] = tickets_from_perform_order
            else:
                print("⚠️ 精确HTML类方法未找到票档信息")

            # 提取城市信息
            print("🌆 提取城市信息")
            cities_found = self.extract_cities_from_html(content)
            if len(cities_found) > 1:
                performance_data["has_city_selection"] = True
                performance_data["cities"] = [
                    {"name": city, "id": f"city_{i}"}
                    for i, city in enumerate(cities_found)
                ]
                print(f"✅ 发现城市选择: {list(cities_found)}")
            else:
                performance_data["has_city_selection"] = False
                if cities_found:
                    performance_data["cities"] = [
                        {"name": cities_found[0], "id": "city_0"}
                    ]
                    print(f"✅ 发现单个城市: {cities_found[0]}")
                else:
                    print("⚠️ 未找到城市信息")

            # 输出解析结果
            print(f"✅ 演出信息解析完成：")
            print(f"   🌆 城市数量: {len(performance_data['cities'])}")
            print(f"   📅 日期数量: {len(performance_data['dates'])}")
            print(f"   🎭 场次数量: {len(performance_data['sessions'])}")
            print(f"   🎫 票档数量: {len(performance_data['ticket_tiers'])}")
            print(f"   🔗 有城市选择: {performance_data['has_city_selection']}")
            print(f"   📆 有日期选择: {performance_data['has_date_selection']}")

        except Exception as e:
            print(f"❌ 解析演出日程失败: {e}")
            import traceback

            traceback.print_exc()

        return performance_data

    def extract_cities_from_html(self, content: str) -> list:
        """从HTML中提取城市信息（基于class名称）"""
        cities_found = []

        try:
            print("🔍 从HTML中提取城市信息...")

            # 方法1: 查找citylist容器中的cityitem
            citylist_pattern = r'<div[^>]*class="[^"]*citylist[^"]*"[^>]*>(.*?)</div>'
            citylist_match = re.search(
                citylist_pattern, content, re.IGNORECASE | re.DOTALL
            )

            if citylist_match:
                citylist_content = citylist_match.group(1)
                # 提取所有cityitem
                cityitem_pattern = (
                    r'<div[^>]*class="[^"]*cityitem[^"]*"[^>]*>([^<]+)</div>'
                )
                city_matches = re.findall(
                    cityitem_pattern, citylist_content, re.IGNORECASE
                )
                cities_found.extend(
                    [city.strip() for city in city_matches if city.strip()]
                )
                print(f"   方法1(citylist): 找到 {len(city_matches)} 个城市")

            # 方法2: 直接查找所有cityitem（作为备用）
            if not cities_found:
                cityitem_pattern = (
                    r'<div[^>]*class="[^"]*cityitem[^"]*"[^>]*>([^<]+)</div>'
                )
                city_matches = re.findall(cityitem_pattern, content, re.IGNORECASE)
                cities_found.extend(
                    [city.strip() for city in city_matches if city.strip()]
                )
                print(f"   方法2(cityitem): 找到 {len(city_matches)} 个城市")

            # 方法3: JSON数据中的cityName（原有逻辑作为备用）
            if not cities_found:
                city_patterns = [
                    r'"cityName":"([^"]+)"',
                    r"城市：([^<\n]+)",
                    r'<div[^>]*class="[^"]*city[^"]*"[^>]*>([^<]+)</div>',
                ]

                cities_set = set()
                for pattern in city_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    cities_set.update(matches)
                cities_found = list(cities_set)
                print(f"   方法3(JSON/通用): 找到 {len(cities_found)} 个城市")

            # 去重并保持顺序
            unique_cities = []
            seen = set()
            for city in cities_found:
                if city not in seen:
                    unique_cities.append(city)
                    seen.add(city)

            if unique_cities:
                print(f"✅ 最终找到城市: {unique_cities}")
            else:
                print("❌ 未找到任何城市信息")

            return unique_cities

        except Exception as e:
            print(f"❌ 从HTML提取城市信息失败: {e}")
            return []

    def extract_sessions_from_html(self, content: str, performance_data: Dict):
        """从HTML中提取场次和票档信息（当没有JSON数据时使用）"""
        try:
            print("🔍 尝试从HTML中提取场次信息...")

            # 方法1: 从HTML class中提取场次信息
            sessions_found = self.extract_sessions_by_class(content)

            # 方法2: 如果没有找到，使用原有的模式匹配
            if not sessions_found:
                # 提取场次信息的多种模式
                session_patterns = [
                    # 大麦网常见的场次时间模式
                    r"(\d{4}-\d{1,2}-\d{1,2})\s*([周一二三四五六日])\s*(\d{1,2}:\d{2})",
                    r"(\d{1,2}月\d{1,2}日)\s*([周一二三四五六日])\s*(\d{1,2}:\d{2})",
                    r"(\d{1,2}\.\d{1,2})\s*([周一二三四五六日])\s*(\d{1,2}:\d{2})",
                    # 直接的时间模式
                    r"时间[：:]\s*([^<\n]+)",
                    r"演出时间[：:]\s*([^<\n]+)",
                ]

                sessions_from_patterns = []
                for pattern in session_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple) and len(match) >= 3:
                            date_part = match[0]
                            weekday = match[1] if len(match) > 1 else ""
                            time_part = match[2] if len(match) > 2 else ""

                            # 构建场次时间
                            if re.match(r"\d{4}-\d{1,2}-\d{1,2}", date_part):
                                # 已经是完整日期格式
                                session_time = f"{date_part} {weekday} {time_part}"
                            elif re.match(r"\d{1,2}月\d{1,2}日", date_part):
                                # 月日格式，需要加上年份
                                current_year = datetime.now().year
                                month_day = re.search(
                                    r"(\d{1,2})月(\d{1,2})日", date_part
                                )
                                if month_day:
                                    month = int(month_day.group(1))
                                    day = int(month_day.group(2))
                                    session_time = f"{current_year}-{month:02d}-{day:02d} {weekday} {time_part}"
                            elif re.match(r"\d{1,2}\.\d{1,2}", date_part):
                                # 点分格式，需要加上年份
                                current_year = datetime.now().year
                                month_day = re.search(
                                    r"(\d{1,2})\.(\d{1,2})", date_part
                                )
                                if month_day:
                                    month = int(month_day.group(1))
                                    day = int(month_day.group(2))
                                    session_time = f"{current_year}-{month:02d}-{day:02d} {weekday} {time_part}"
                            else:
                                session_time = (
                                    f"{date_part} {weekday} {time_part}".strip()
                                )

                            sessions_from_patterns.append(
                                {
                                    "session_time": session_time,
                                    "time": time_part,
                                    "date_display": date_part,
                                }
                            )
                        elif isinstance(match, str):
                            # 单个字符串匹配
                            sessions_from_patterns.append(
                                {
                                    "session_time": match,
                                    "time": "未指定时间",
                                    "date_display": match,
                                }
                            )

                sessions_found.extend(sessions_from_patterns)

            # 如果没有找到详细的场次信息，查找基本的时间信息
            if not sessions_found:
                basic_time_patterns = [
                    r'<div[^>]*class="[^"]*perform[^"]*time[^"]*"[^>]*>([^<]+)</div>',
                    r'<span[^>]*class="[^"]*time[^"]*"[^>]*>([^<]+)</span>',
                    r"演出时间[：:]([^<\n]+)",
                    # 移除通用的"时间:"模式，避免匹配须知信息
                ]

                for pattern in basic_time_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        # 更严格的过滤，排除须知信息
                        if (
                            match
                            and len(match.strip()) > 3
                            and not re.search(
                                r"入场|迟到|开演.*分钟|禁止|携带|寄存", match
                            )
                            and not re.search(
                                r"具体时间点|舞台监督|通知|准|拍摄", match
                            )
                        ):
                            sessions_found.append(
                                {
                                    "id": f"session_{len(sessions_found)}",
                                    "name": match.strip(),
                                    "session_time": match.strip(),
                                    "time": "未指定时间",
                                    "date_display": match.strip(),
                                    "city": "默认城市",
                                }
                            )

            # 处理找到的场次信息
            for i, session in enumerate(sessions_found):
                session_id = f"html_session_{i}"
                session_info = {
                    "id": session_id,
                    "name": session.get("session_time", ""),
                    "time": session.get("time", ""),
                    "session_time": session.get("session_time", ""),
                    "date_display": session.get("date_display", ""),
                    "city": "默认城市",
                }

                # 添加到场次列表
                performance_data["sessions"].append(session_info)

                # 提取该场次的票档信息
                ticket_info = self.extract_general_ticket_info(content)

                # 建立关联关系（简化结构，直接以session_id为key）
                performance_data["schedule_matrix"][session_id] = {
                    "session": session_info,
                    "tickets": ticket_info,
                }

            # 如果找到了场次信息，设置基本标志
            if sessions_found:
                print(f"✅ 从HTML中提取到 {len(sessions_found)} 个场次")
                # 保持已经设置的城市和日期选择状态，不要重新设置为False
                if "has_date_selection" not in performance_data:
                    performance_data["has_date_selection"] = False
            else:
                print("❌ 未能从HTML中提取到场次信息")

        except Exception as e:
            print(f"❌ 从HTML提取场次信息失败: {e}")

    def _build_full_date(self, date_name: str, month: str) -> str:
        """构建完整的日期格式：2025-07-20 星期日"""
        try:
            # 解析日期名称，如 "07.20 周日"
            date_match = re.search(
                r"(\d{2})\.(\d{2})\s+(周[一二三四五六日])", date_name
            )
            if date_match:
                month_day = int(date_match.group(1))
                day_day = int(date_match.group(2))
                weekday = date_match.group(3)

                # 从month参数中提取年份，如 "2025-08"
                year_match = re.search(r"(\d{4})", month)
                if year_match:
                    year = int(year_match.group(1))
                else:
                    # 如果没有找到年份，使用当前年份
                    year = datetime.now().year

                # 构建完整日期
                full_date = f"{year}-{month_day:02d}-{day_day:02d} {weekday}"
                return full_date
        except Exception as e:
            print(f"❌ 构建日期格式失败: {e}")

        return date_name

    def _build_session_time(
        self, full_date: str, begin_time: str, full_time: str
    ) -> str:
        """构建完整的场次时间格式：2025-07-20 星期五 19:30"""
        try:
            # 如果full_date已经包含完整格式，直接使用
            if re.match(r"\d{4}-\d{2}-\d{2}\s+星期[一二三四五六日]", full_date):
                date_part = full_date
            else:
                # 否则使用full_date作为基础
                date_part = full_date

            # 提取时间部分
            time_part = begin_time if begin_time else "19:30"  # 默认时间

            # 组合完整格式
            session_time = f"{date_part} {time_part}"
            return session_time
        except Exception as e:
            print(f"❌ 构建场次时间格式失败: {e}")

        return full_time

    def extract_tickets_for_perform(self, content: str, perform_id: str) -> List[Dict]:
        """为特定场次提取票档信息"""
        tickets = []

        try:
            # 方法1: 从perform JSON数据中查找seatPlanId
            perform_pattern = rf'"performId":{perform_id}[^}}]*"seatPlanId":(\d+)'
            seat_plan_match = re.search(perform_pattern, content)

            if seat_plan_match:
                seat_plan_id = seat_plan_match.group(1)
                # 使用seatPlanId查找对应的票档信息
                tickets = self.extract_tickets_by_seat_plan(content, seat_plan_id)

            # 方法2: 如果没有找到seatPlanId，使用通用票档解析
            if not tickets:
                tickets = self.extract_general_ticket_info(content)

            # 方法3: 从优惠信息中补充票档数据
            promotion_tickets = self.extract_promotion_tickets(content)

            # 合并和去重票档信息
            all_tickets = tickets + promotion_tickets
            unique_tickets = []
            seen_prices = set()

            for ticket in all_tickets:
                price = ticket.get("price", 0)
                if price and price not in seen_prices:
                    unique_tickets.append(ticket)
                    seen_prices.add(price)

            return sorted(unique_tickets, key=lambda x: x.get("price", 0))

        except Exception as e:
            print(f"❌ 为场次 {perform_id} 提取票档失败: {e}")
            return []

    def extract_tickets_by_seat_plan(
        self, content: str, seat_plan_id: str
    ) -> List[Dict]:
        """根据座位计划ID提取票档信息"""
        tickets = []

        try:
            # 查找seatPlanId对应的票档数据
            seat_plan_pattern = rf'"seatPlanId":{seat_plan_id}[^}}]*"price":(\d+)'
            price_matches = re.findall(seat_plan_pattern, content)

            for price in price_matches:
                tickets.append(
                    {
                        "price": int(price),
                        "seat_plan_id": seat_plan_id,
                        "tier": f"{price}元",
                        "description": f"{price}元票档",
                        "status": "可购买",
                    }
                )

        except Exception as e:
            print(f"❌ 根据座位计划 {seat_plan_id} 提取票档失败: {e}")

        return tickets

    def extract_promotion_tickets(self, content: str) -> List[Dict]:
        """从优惠信息中提取票档"""
        tickets = []

        try:
            # 从perform__ticketinfo中提取优惠票档信息
            promotion_patterns = [
                r"单人早鸟(\d+)元[^)]*原价(\d+)[^)]*",
                r"早鸟特惠([0-9.]+)元[^)]*原价(\d+)[^)]*",
                r"单人优惠票(\d+)元[^)]*原价(\d+)[^)]*",
                r"双人套票(\d+)元[^)]*原价(\d+)",
            ]

            for pattern in promotion_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    promotion_price = float(match[0])
                    original_price = int(match[1])

                    tickets.append(
                        {
                            "price": int(promotion_price),
                            "original_price": original_price,
                            "tier": f"早鸟{int(promotion_price)}元",
                            "description": f"早鸟{int(promotion_price)}元（原价{original_price}）",
                            "status": "早鸟优惠",
                            "savings": original_price - int(promotion_price),
                        }
                    )

                    # 同时添加原价票
                    if not any(t.get("price") == original_price for t in tickets):
                        tickets.append(
                            {
                                "price": original_price,
                                "tier": f"{original_price}元",
                                "description": f"{original_price}元",
                                "status": "原价票",
                            }
                        )

        except Exception as e:
            print(f"❌ 提取优惠票档失败: {e}")

        return tickets

    def extract_general_ticket_info(self, content: str) -> List[Dict]:
        """通用票档信息提取（从HTML页面结构中提取）"""
        tickets = []

        try:
            # 从页面显示的票档区域提取（改进版，增加更多class模式）
            ticket_patterns = [
                # 原有模式
                r"(\d+)元[\\s\\S]*?(缺货登记|缺货|售罄|可购买|立即购买|选座购票)",
                r'class="perform__order__price"[^>]*>(\d+)</span>',
                r'data-price="(\d+)"',
                # 新增基于class的模式
                r'<span[^>]*class="[^"]*price[^"]*"[^>]*>(\d+)</span>',
                r'<div[^>]*class="[^"]*price[^"]*"[^>]*>(\d+)元?</div>',
                r'<li[^>]*class="[^"]*price[^"]*"[^>]*>[^<]*?(\d+)元?[^<]*?</li>',
                # 票档相关class
                r'<div[^>]*class="[^"]*ticket[^"]*"[^>]*>[^<]*?(\d+)元?[^<]*?</div>',
                r'<span[^>]*class="[^"]*ticket[^"]*price[^"]*"[^>]*>(\d+)</span>',
                # 订单价格相关
                r'<span[^>]*class="[^"]*order[^"]*price[^"]*"[^>]*>(\d+)</span>',
                r'<div[^>]*class="[^"]*order[^"]*price[^"]*"[^>]*>(\d+)</div>',
            ]

            for pattern in ticket_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                for match in matches:
                    if isinstance(match, tuple):
                        price = int(match[0])
                        status = match[1] if len(match) > 1 else "可购买"
                    else:
                        price = int(match)
                        status = "可购买"

                    if 10 <= price <= 10000:  # 价格合理性检查
                        tickets.append(
                            {
                                "price": price,
                                "tier": f"{price}元",
                                "description": f"{price}元",
                                "status": status,
                            }
                        )

        except Exception as e:
            print(f"❌ 通用票档提取失败: {e}")

        return tickets

    def extract_tickets_by_perform_order_class(self, content: str) -> List[Dict]:
        """通过 perform__order__select 类精确提取票档信息"""
        tickets = []

        try:
            # 精确定位票档选择区域（不包含场次的那个）
            # 先找到所有的 perform__order__select，然后排除 perform__order__select__performs
            all_sections = re.findall(
                r'<div class="perform__order__select(?! perform__order__select__performs)[^"]*">(.*?)</div>\s*</div>',
                content,
                re.DOTALL,
            )

            for section in all_sections:
                # 检查是否包含"票档"文字
                if "票档" in section:
                    self.log("✅ 找到票档选择区域")

                    # 提取所有票档项
                    ticket_pattern = r'<div class="select_right_list_item[^"]*sku_item[^"]*"[^>]*>(.*?)</div>'
                    ticket_matches = re.findall(ticket_pattern, section, re.DOTALL)

                    for ticket_html in ticket_matches:
                        # 提取价格信息
                        price_pattern = r'<div class="skuname">\s*([^<\n]+)'
                        price_match = re.search(price_pattern, ticket_html)

                        if price_match:
                            price_text = price_match.group(1).strip()

                            # 解析价格（支持多种格式）
                            price_value = 0
                            original_price = None
                            ticket_type = "单人票"

                            # 匹配基本价格格式：180元, 1000元双人套票（580*2）
                            if "双人套票" in price_text or "套票" in price_text:
                                ticket_type = "双人套票"
                                price_match = re.search(r"(\d+)元", price_text)
                                if price_match:
                                    price_value = int(price_match.group(1))
                            else:
                                price_match = re.search(r"(\d+)元", price_text)
                                if price_match:
                                    price_value = int(price_match.group(1))

                            # 提取状态信息
                            status = "可购买"
                            if "notticket" in ticket_html or "缺货" in ticket_html:
                                status = "缺货登记"
                            elif "presell" in ticket_html or "惠" in ticket_html:
                                status = "优惠"

                            if price_value > 0:
                                ticket_info = {
                                    "price": price_value,
                                    "original_price": original_price,
                                    "status": status,
                                    "type": ticket_type,
                                    "name": price_text,
                                }

                                tickets.append(ticket_info)
                                self.log(f"✅ 解析票档: {price_text} - {status}")

                    break  # 找到票档区域后退出

            if not tickets:
                self.log("❌ 未找到票档选择区域")

        except Exception as e:
            self.log(f"❌ 通过HTML类提取票档失败: {e}")

        return tickets

    def build_complete_schedule_matrix(
        self,
        cities: List[Dict],
        dates: List[str],
        sessions: List[Dict],
        tickets: List[Dict],
    ) -> Dict:
        """构建完整的城市-日期-场次-票档依存关系矩阵"""
        schedule_matrix = {}

        try:
            if not cities:  # 无城市选择
                if dates and len(dates) > 1:  # 有多个日期
                    # 按日期组织
                    for date in dates:
                        schedule_matrix[date] = {}
                        # 找到该日期的所有场次
                        date_sessions = [s for s in sessions if s.get("date") == date]
                        for session in date_sessions:
                            schedule_matrix[date][session["id"]] = {
                                "session": session,
                                "tickets": tickets,  # 所有场次共享票档
                            }
                else:
                    # 扁平结构：直接按场次组织
                    for session in sessions:
                        schedule_matrix[session["id"]] = {
                            "session": session,
                            "tickets": tickets,
                        }
            else:  # 有城市选择
                for city in cities:
                    city_id = city.get("id", city.get("name"))
                    schedule_matrix[city_id] = {}

                    if dates and len(dates) > 1:  # 城市下有多个日期
                        for date in dates:
                            schedule_matrix[city_id][date] = {}
                            # 找到该城市该日期的所有场次
                            city_date_sessions = [
                                s
                                for s in sessions
                                if s.get("date") == date
                                and s.get("city") == city.get("name")
                            ]
                            for session in city_date_sessions:
                                schedule_matrix[city_id][date][session["id"]] = {
                                    "session": session,
                                    "tickets": tickets,
                                }
                    else:  # 城市下直接是场次
                        city_sessions = [
                            s for s in sessions if s.get("city") == city.get("name")
                        ]
                        for session in city_sessions:
                            schedule_matrix[city_id][session["id"]] = {
                                "session": session,
                                "tickets": tickets,
                            }

            self.log(f"✅ 构建完整关联矩阵，结构: {type(schedule_matrix)}")
            return schedule_matrix

        except Exception as e:
            self.log(f"❌ 构建关联矩阵失败: {e}")
            # 返回扁平结构作为fallback
            fallback_matrix = {}
            for session in sessions:
                fallback_matrix[session["id"]] = {
                    "session": session,
                    "tickets": tickets,
                }
            return fallback_matrix

    def extract_short_name(self, title: str) -> str:
        """从标题中提取短剧名"""
        if not title or not isinstance(title, str):
            return ""

        # 清理标题，移除多余空格
        clean_title = title.strip().replace("  ", " ")

        # 规则1: 提取《》内的内容
        book_title_match = re.search(r"《([^》]+)》", clean_title)
        if book_title_match:
            return book_title_match.group(1).strip()

        # 规则2: 提取【】内的内容
        bracket_match = re.search(r"【([^】]+)】", clean_title)
        if bracket_match:
            return bracket_match.group(1).strip()

        # 规则3: 提取""内的内容
        quote_match = re.search(r'"([^"]+)"', clean_title)
        if quote_match:
            return quote_match.group(1).strip()

        # 规则4: 提取''内的内容
        single_quote_match = re.search(r"'([^']+)'", clean_title)
        if single_quote_match:
            return single_quote_match.group(1).strip()

        # 规则5: 移除常见后缀词汇后的内容
        suffix_patterns = [
            r"(.+?)(?:\s*[-—]\s*.*)?$",  # 移除破折号后的内容
            r"(.+?)(?:\s*\|\s*.*)?$",  # 移除竖线后的内容
            r"(.+?)(?:\s*话剧.*)?$",  # 移除"话剧"后的内容
            r"(.+?)(?:\s*音乐剧.*)?$",  # 移除"音乐剧"后的内容
            r"(.+?)(?:\s*演唱会.*)?$",  # 移除"演唱会"后的内容
            r"(.+?)(?:\s*音乐会.*)?$",  # 移除"音乐会"后的内容
            r"(.+?)(?:\s*\d{4}.*)?$",  # 移除年份后的内容
        ]

        for pattern in suffix_patterns:
            match = re.search(pattern, clean_title)
            if match and match.group(1) and match.group(1).strip() != clean_title:
                extracted = match.group(1).strip()
                if 2 <= len(extracted) <= 30:
                    return extracted

        # 规则6: 如果标题较短且没有特殊符号，直接使用
        if len(clean_title) <= 20 and not re.search(
            r"[（）()【】《》" "'']", clean_title
        ):
            return clean_title

        # 规则7: 提取第一个词或短语（最多15个字符）
        first_part = clean_title[:15]
        space_index = first_part.find(" ")
        if 0 < space_index < 15:
            return first_part[:space_index]

        # 默认返回前15个字符
        return clean_title[:15]

    def extract_detail_info(self, content: str, item_id: str, url: str) -> Dict:
        """从演出详情页内容中提取完整信息"""
        detail_info = {
            "id": item_id,
            "url": url,
            "crawl_time": datetime.now().isoformat(),
            "title": "待解析",
            "shortName": "",  # 新增：短剧名字段
            "time_period": "待解析",
            "venue": "待解析",
            "poster": "",
            "cities": [],
            "dates": [],
            "sessions": [],
            "ticket_tiers": [],
            "schedule_matrix": {},
            "has_city_selection": False,
            "has_date_selection": False,
            "show_description": "待解析",
            "purchase_notes": {},
            "viewing_notes": {},
            "images": [],
            "raw_content_length": len(content),
            "cast_info": [],
            "notices": [],
            "performance_info": {},
            "calendar_sessions": [],  # 新增：通过日历控件获取的场次
        }

        try:
            self.log(f"🔍 开始解析详情页: {item_id}")

            # 1. 智能检测日历组件并选择提取策略
            self.log("🔍 步骤1: 智能检测日历组件")
            calendar_detection = None
            calendar_sessions = []

            if self.page:  # 只有在使用Playwright时才检测
                calendar_detection = self._detect_calendar_presence()

                if calendar_detection["has_calendar"]:
                    self.log("🎯 检测到日历组件，使用日历优先策略")
                    # 使用智能日历提取
                    calendar_sessions = self._smart_calendar_extraction(
                        url, item_id, calendar_detection
                    )

                    if calendar_sessions:
                        self.log(
                            f"✅ 日历提取成功，获得 {len(calendar_sessions)} 个场次"
                        )
                        # 从日历场次中提取基础数据
                        detail_info["sessions"] = calendar_sessions
                        calendar_dates = list(
                            set(
                                [
                                    s.get("date", "")
                                    for s in calendar_sessions
                                    if s.get("date")
                                ]
                            )
                        )
                        if calendar_dates:
                            detail_info["dates"] = calendar_dates
                            detail_info["has_date_selection"] = len(calendar_dates) > 1
                        detail_info["calendar_sessions"] = calendar_sessions
                    else:
                        self.log("⚠️ 日历提取失败，降级到HTML解析")
                        calendar_detection = None  # 标记为无日历，使用HTML解析
                else:
                    self.log("ℹ️ 无日历组件，使用HTML解析策略")
            else:
                self.log("ℹ️ 非Playwright模式，使用HTML解析策略")

            # 2. 如果没有日历或日历提取失败，使用HTML解析
            if not calendar_detection or not calendar_sessions:
                self.log("📋 步骤2: 执行HTML解析策略")
                performance_schedule = self.extract_performance_schedule(content)
                # 只在没有日历数据时更新session相关信息
                if not calendar_sessions:
                    detail_info.update(performance_schedule)
                else:
                    # 如果有日历数据，只更新非session相关的信息
                    for key, value in performance_schedule.items():
                        if key not in ["sessions", "dates", "has_date_selection"]:
                            detail_info[key] = value
            else:
                self.log("✅ 跳过HTML解析，已通过日历获取场次数据")

            # 3. 优先使用精确的HTML类定位提取剧目介绍
            self.log("📖 步骤3: 精确提取剧目介绍")
            description_from_detail_class = (
                self.extract_show_description_by_detail_class(content)
            )
            if description_from_detail_class and description_from_detail_class.get(
                "show_description"
            ):
                self.log(f"✅ 通过精确HTML类提取到剧目介绍")
                # 更新详情信息
                if description_from_detail_class.get("show_description"):
                    detail_info["show_description"] = description_from_detail_class[
                        "show_description"
                    ]
                if description_from_detail_class.get("images"):
                    detail_info["images"] = description_from_detail_class["images"]
                if description_from_detail_class.get("performance_info"):
                    detail_info["performance_info"] = description_from_detail_class[
                        "performance_info"
                    ]
                if description_from_detail_class.get("cast_info"):
                    detail_info["cast_info"] = description_from_detail_class[
                        "cast_info"
                    ]
                if description_from_detail_class.get("notices"):
                    detail_info["notices"] = description_from_detail_class["notices"]
            else:
                self.log("⚠️ 精确HTML类方法未找到剧目介绍")

            # 4. 提取标题 - 使用BeautifulSoup精确提取
            self.log("📝 步骤4: 精确提取标题")
            self.log(f"   内容长度: {len(content)}")

            try:
                # 检查BeautifulSoup导入
                try:
                    from bs4 import BeautifulSoup

                    self.log("✅ BeautifulSoup导入成功")
                except ImportError as e:
                    self.log(f"❌ BeautifulSoup导入失败: {e}")
                    raise e

                soup = BeautifulSoup(content, "html.parser")
                self.log("✅ BeautifulSoup解析成功")

                # 策略1: 从JSON数据中提取（最准确）
                self.log("📋 策略1: 从JSON数据中提取")
                json_title = None
                json_patterns = [
                    r'"itemName":"([^"]+)"',
                    r'"performName":"([^"]+)"',
                    r'"title":"([^"]+)"',
                    r'"name":"([^"]+)"',
                ]

                for i, pattern in enumerate(json_patterns):
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        json_title = match.group(1).strip()
                        self.log(f"✅ 找到JSON匹配 {i+1}: {json_title}")
                        if json_title and len(json_title) > 3:
                            # 清理JSON标题
                            json_title = re.sub(
                                r"【网上订票】|【网络订票】", "", json_title
                            )
                            json_title = re.sub(r"-\s*大麦网.*$", "", json_title)
                            json_title = json_title.strip()
                            self.log(f"✅ JSON标题清理后: {json_title}")
                            break
                        else:
                            self.log(f"⚠️ JSON标题长度不足: {len(json_title)}")
                    else:
                        self.log(f"❌ JSON模式 {i+1} 未匹配")

                if json_title:
                    detail_info["title"] = json_title
                    self.log(f"✅ 最终使用JSON标题: {json_title}")
                else:
                    self.log("❌ JSON策略失败，尝试策略2")

                    # 策略2: 从meta标签中提取
                    self.log("📋 策略2: 从meta标签中提取")
                    meta_title = None
                    meta_description = soup.find("meta", attrs={"name": "description"})
                    if meta_description and meta_description.get("content"):
                        desc_content = meta_description["content"]
                        self.log(f"📄 找到meta描述: {desc_content[:100]}...")

                        # 从描述中提取标题（通常描述包含完整标题）
                        if "大麦网" in desc_content and "将于" in desc_content:
                            # 提取"【城市】演出名称"格式
                            title_match = re.search(
                                r"【([^】]+)】([^，。]*?)(?:，|。|将于)", desc_content
                            )
                            if title_match:
                                city = title_match.group(1)
                                show_name = title_match.group(2).strip()
                                meta_title = f"【{city}】{show_name}"
                                self.log(f"✅ 从meta描述提取标题: {meta_title}")
                            else:
                                self.log("❌ 无法从meta描述提取标题")
                        else:
                            self.log("❌ meta描述格式不符合预期")
                    else:
                        self.log("❌ 未找到meta描述标签")

                    if meta_title:
                        detail_info["title"] = meta_title
                        self.log(f"✅ 最终使用meta标题: {meta_title}")
                    else:
                        self.log("❌ Meta策略失败，尝试策略3")

                        # 策略3: 从title标签中提取
                        self.log("📋 策略3: 从title标签中提取")
                        title_tag = soup.find("title")
                        if title_tag and title_tag.string:
                            title_text = title_tag.string.strip()
                            self.log(f"📄 原始title: {title_text}")

                            # 清理标题
                            title_text = re.sub(
                                r"【网上订票】|【网络订票】", "", title_text
                            )
                            title_text = re.sub(r"-\s*大麦网.*$", "", title_text)
                            title_text = re.sub(r"\s*-\s*大麦网\s*$", "", title_text)
                            title_text = title_text.strip()

                            self.log(f"📄 清理后title: {title_text}")

                            if title_text and len(title_text) > 3:
                                detail_info["title"] = title_text
                                self.log(f"✅ 最终使用title标签标题: {title_text}")
                            else:
                                self.log(f"❌ title标签标题长度不足: {len(title_text)}")
                                self.log("❌ Title策略失败，尝试策略4")

                                # 策略4: 从页面中的特定HTML元素提取
                                self.log("📋 策略4: 从HTML元素中提取")
                                title_elements = [
                                    soup.find(
                                        "span", attrs={"data-spm-anchor-id": True}
                                    ),
                                    soup.find("div", class_="title"),
                                    soup.find("h1"),
                                    soup.find("h2"),
                                    soup.find("div", class_="perform__title"),
                                    soup.find("div", class_="item-title"),
                                ]

                                for i, element in enumerate(title_elements):
                                    if element and element.get_text().strip():
                                        element_title = element.get_text().strip()
                                        self.log(
                                            f"✅ 从元素{i+1}提取标题: {element_title}"
                                        )
                                        if element_title and len(element_title) > 3:
                                            detail_info["title"] = element_title
                                            self.log(
                                                f"✅ 最终使用HTML元素标题: {element_title}"
                                            )
                                            break
                                        else:
                                            self.log(
                                                f"⚠️ 元素{i+1}标题长度不足: {len(element_title)}"
                                            )
                                    else:
                                        self.log(f"❌ 元素{i+1}未找到或为空")
                                else:
                                    self.log("❌ HTML元素策略失败，尝试策略5")

                # 如果所有策略都失败，使用正则表达式作为最后的备选
                if detail_info["title"] == "待解析":
                    self.log("📋 策略5: 正则表达式备选")
                    title_patterns = [
                        r"<title>([^<]+?)【网上订票】[^<]*</title>",
                        r"<title>([^<]+?)【网络订票】[^<]*</title>",
                        r"<title>([^<]+?)-[^<]*大麦网[^<]*</title>",
                        r"<title>([^<]+)</title>",
                    ]

                    for i, pattern in enumerate(title_patterns):
                        match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
                        if match:
                            title = match.group(1).strip()
                            self.log(f"✅ 正则模式{i+1}匹配: {title}")
                            # 清理标题
                            title = re.sub(r"【网上订票】|【网络订票】", "", title)
                            title = re.sub(r"-\s*大麦网.*$", "", title)
                            title = title.strip()
                            if title and len(title) > 3:
                                detail_info["title"] = title
                                self.log(f"✅ 最终使用正则表达式标题: {title}")
                                break
                            else:
                                self.log(f"⚠️ 正则模式{i+1}标题长度不足: {len(title)}")
                        else:
                            self.log(f"❌ 正则模式{i+1}未匹配")
                    else:
                        self.log("❌ 所有标题提取策略都失败了")

            except Exception as e:
                self.log(f"⚠️ 标题提取异常: {e}")
                import traceback

                self.log(f"⚠️ 异常详情: {traceback.format_exc()}")

                # 异常情况下的备选方案
                self.log("📋 异常情况下的备选方案")
                title_match = re.search(
                    r"<title>([^<]+)</title>", content, re.IGNORECASE
                )
                if title_match:
                    title = title_match.group(1).strip()
                    title = re.sub(r"【网上订票】|【网络订票】", "", title)
                    title = re.sub(r"-\s*大麦网.*$", "", title)
                    title = title.strip()
                    if title and len(title) > 3:
                        detail_info["title"] = title
                        self.log(f"✅ 异常情况下提取标题: {title}")
                    else:
                        self.log(f"❌ 异常情况下标题长度不足: {len(title)}")
                else:
                    self.log("❌ 异常情况下也未找到title标签")

            self.log(f"🔍 标题提取完成，最终结果: {detail_info['title']}")

            # 4.5. 生成短剧名
            self.log("🎭 步骤4.5: 生成短剧名")
            if detail_info["title"] and detail_info["title"] != "待解析":
                short_name = self.extract_short_name(detail_info["title"])
                detail_info["shortName"] = short_name
                self.log(f"   ✅ 短剧名: {short_name}")
            else:
                detail_info["shortName"] = ""
                self.log("   ⚠️ 标题为空，无法生成短剧名")

            # 5. 补充提取基础信息（海报、场馆、时间段）
            self.log("🏛️ 步骤5: 补充提取基础信息")

            # 提取海报 - 优先选择class="poster"的图片
            if not detail_info.get("poster"):
                poster_patterns = [
                    r'<img[^>]*class="poster"[^>]*(?:data-src|src)="([^"]+)"',  # 优先级最高
                    r'<img[^>]*(?:data-src|src)="([^"]*(?:poster|cover|banner)[^"]*)"',
                    r'<img[^>]*(?:data-src|src)="(//img\.alicdn\.com/[^"]*)"',  # alicdn图片
                    r'"posterUrl":"([^"]+)"',  # JSON中的海报URL
                ]

                for pattern in poster_patterns:
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        poster_url = match.group(1).strip()
                        if poster_url:
                            # 确保URL完整性
                            if poster_url.startswith("//"):
                                poster_url = "https:" + poster_url
                            elif not poster_url.startswith("http"):
                                poster_url = "https://" + poster_url
                            detail_info["poster"] = poster_url
                            self.log(f"   ✅ 海报: {poster_url}")
                            break

            # 提取场馆信息（如果基础数据没有获取到）
            if detail_info["venue"] == "待解析":
                venue_patterns = [
                    r"场馆：([^<\n]+)",
                    r'"venueName":"([^"]+)"',
                    r'<div[^>]*class="[^"]*venue[^"]*"[^>]*>([^<]+)</div>',
                ]

                for pattern in venue_patterns:
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        venue = match.group(1).strip()
                        if venue and len(venue) > 2:
                            detail_info["venue"] = venue
                            self.log(f"   ✅ 场馆: {venue}")
                            break

            # 提取时间段（如果基础数据没有获取到）
            if detail_info["time_period"] == "待解析":
                time_patterns = [
                    r"时间：([^<\n]+)",
                    r'"performTime":"([^"]+)"',
                    r'<div[^>]*class="[^"]*time[^"]*"[^>]*>([^<]+)</div>',
                ]

                for pattern in time_patterns:
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        time_period = match.group(1).strip()
                        if time_period and len(time_period) > 5:
                            detail_info["time_period"] = time_period
                            self.log(f"   ✅ 时间段: {time_period}")
                            break

            # 6. 构建完整的城市-日期-场次-票档依存关系矩阵
            self.log("🔗 步骤6: 构建调度矩阵")
            if not detail_info["sessions"]:
                self.log("⚠️ 未找到场次信息，跳过创建默认场次")

            detail_info["schedule_matrix"] = self.build_complete_schedule_matrix(
                detail_info["cities"],
                detail_info["dates"],
                detail_info["sessions"],
                detail_info["ticket_tiers"],
            )

            self.log(f"✅ 详情信息解析完成: {detail_info['title']}")
            self.log(f"   📅 日期数量: {len(detail_info['dates'])}")
            self.log(f"   🎭 场次数量: {len(detail_info['sessions'])}")
            self.log(f"   🎫 票档数量: {len(detail_info['ticket_tiers'])}")
            self.log(f"   🏛️ 场馆: {detail_info['venue']}")
            self.log(f"   🖼️ 图片数量: {len(detail_info['images'])}")
            self.log(f"   👥 主创信息: {len(detail_info['cast_info'])} 人")

        except Exception as e:
            self.log(f"❌ 详情信息解析失败: {e}")
            import traceback

            traceback.print_exc()

        return detail_info

    def extract_sessions_by_perform_order_class(self, content: str) -> List[Dict]:
        """基于BeautifulSoup精准定位并提取场次信息"""
        sessions = []

        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(content, "html.parser")

            # 方法1: 精确定位 perform__order__select perform__order__select__performs
            perform_div = None

            # 查找包含这两个class的div
            for div in soup.find_all("div"):
                class_list = div.get("class", [])
                if (
                    "perform__order__select" in class_list
                    and "perform__order__select__performs" in class_list
                ):
                    perform_div = div
                    break

            if not perform_div:
                # 方法2: 查找包含关键词的div
                for div in soup.find_all("div"):
                    class_str = " ".join(div.get("class", []))
                    if (
                        "perform__order__select" in class_str
                        and "performs" in class_str
                    ):
                        perform_div = div
                        break

            if perform_div:
                self.log("🔍 找到perform__order__select__performs容器")

                # 在容器内查找所有场次项
                session_items = perform_div.find_all(
                    "div", class_="select_right_list_item"
                )

                if not session_items:
                    # 备选方案1：查找包含select_right_list_item的div（可能有多个class）
                    all_divs = perform_div.find_all("div")
                    for div in all_divs:
                        classes = div.get("class", [])
                        if "select_right_list_item" in classes:
                            session_items.append(div)

                if not session_items:
                    # 备选方案2：查找包含时间格式的div
                    for div in perform_div.find_all("div"):
                        text = div.get_text().strip()
                        if re.search(r"\d{4}-\d{2}-\d{2}.*\d{1,2}:\d{2}", text):
                            session_items.append(div)

                self.log(f"   📅 找到 {len(session_items)} 个场次候选项")

                for i, item in enumerate(session_items):
                    try:
                        # 获取该元素的所有文本内容
                        item_text = item.get_text().strip()

                        # 提取时间信息 - 支持多种格式
                        time_patterns = [
                            r"(\d{4}-\d{2}-\d{2})\s*([星期\u4e00-\u9fa5]+)\s*(\d{1,2}:\d{2})",  # 标准格式
                            r"(\d{4}年\d{1,2}月\d{1,2}日)\s*([星期\u4e00-\u9fa5]+)\s*(\d{1,2}:\d{2})",  # 中文格式
                            r"(\d{1,2}月\d{1,2}日)\s*([星期\u4e00-\u9fa5]+)\s*(\d{1,2}:\d{2})",  # 简化格式
                            r"(\d{4}-\d{2}-\d{2})\s*(\d{1,2}:\d{2})",  # 无星期格式
                        ]

                        time_match = None
                        for pattern in time_patterns:
                            time_match = re.search(pattern, item_text)
                            if time_match:
                                break

                        if time_match:
                            # 根据匹配的模式处理日期格式
                            if len(time_match.groups()) >= 3:
                                date_part = time_match.group(1)
                                weekday = (
                                    time_match.group(2)
                                    if len(time_match.groups()) >= 3
                                    else ""
                                )
                                time_part = (
                                    time_match.group(3)
                                    if len(time_match.groups()) >= 3
                                    else time_match.group(2)
                                )
                            else:
                                date_part = time_match.group(1)
                                weekday = ""
                                time_part = time_match.group(2)

                            # 标准化日期格式
                            if "年" in date_part:
                                # 转换中文日期格式为标准格式
                                date_match = re.search(
                                    r"(\d{4})年(\d{1,2})月(\d{1,2})日", date_part
                                )
                                if date_match:
                                    year, month, day = date_match.groups()
                                    date_part = (
                                        f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                                    )
                            elif re.match(r"^\d{1,2}月\d{1,2}日$", date_part):
                                # 补充年份
                                current_year = datetime.now().year
                                month_day_match = re.search(
                                    r"(\d{1,2})月(\d{1,2})日", date_part
                                )
                                if month_day_match:
                                    month, day = month_day_match.groups()
                                    date_part = f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"

                            # 检查状态和特殊标记
                            class_list = item.get("class", [])
                            class_str = " ".join(class_list)

                            # 检查是否有优惠标记
                            has_discount = (
                                "惠" in item_text
                                or "discount" in class_str
                                or any("优惠" in str(tag) for tag in item.find_all())
                            )

                            # 检查状态
                            is_active = "active" in class_str or "selected" in class_str
                            is_disabled = (
                                "disabled" in class_str
                                or "sold" in class_str
                                or "售罄" in item_text
                            )

                            status = (
                                "disabled"
                                if is_disabled
                                else ("active" if is_active else "available")
                            )

                            # 构建完整的场次时间显示
                            session_time = f"{date_part} {weekday} {time_part}".strip()

                            session_info = {
                                "id": f"session_{i}",
                                "name": session_time,
                                "session_time": session_time,
                                "date": date_part,
                                "weekday": weekday,
                                "time": time_part,
                                "date_display": session_time,
                                "has_discount": has_discount,
                                "status": status,
                                "raw_text": item_text,  # 保留原始文本用于调试
                            }

                            sessions.append(session_info)
                            self.log(
                                f"   ✅ 场次: {session_time} {'(有优惠)' if has_discount else ''} [状态: {status}]"
                            )

                        else:
                            self.log(f"   ⚠️ 无法解析场次时间: {item_text[:50]}...")

                    except Exception as e:
                        self.log(f"   ❌ 解析单个场次失败: {e}")
                        continue

            else:
                self.log("❌ 未找到perform__order__select__performs容器")

                # 备选方案：直接查找包含时间信息的div
                self.log("🔍 尝试备选方案：查找包含时间信息的div")

                # 查找所有包含日期时间格式的div
                all_divs = soup.find_all("div")
                time_pattern = r"\d{4}-\d{2}-\d{2}.*\d{1,2}:\d{2}"

                for i, div in enumerate(all_divs):
                    div_text = div.get_text().strip()
                    if re.search(time_pattern, div_text):
                        # 类似上面的处理逻辑
                        time_match = re.search(
                            r"(\d{4}-\d{2}-\d{2})\s*([星期\u4e00-\u9fa5]+)?\s*(\d{1,2}:\d{2})",
                            div_text,
                        )
                        if time_match:
                            date_part = time_match.group(1)
                            weekday = time_match.group(2) or ""
                            time_part = time_match.group(3)
                            session_time = f"{date_part} {weekday} {time_part}".strip()

                            sessions.append(
                                {
                                    "id": f"fallback_session_{i}",
                                    "name": session_time,
                                    "session_time": session_time,
                                    "date": date_part,
                                    "weekday": weekday,
                                    "time": time_part,
                                    "date_display": session_time,
                                    "has_discount": False,
                                    "status": "available",
                                    "raw_text": div_text,
                                }
                            )
                            self.log(f"   ✅ 备选场次: {session_time}")

        except Exception as e:
            self.log(f"❌ BeautifulSoup提取场次信息失败: {e}")
            # fallback 到原有正则表达式方法
            return self._extract_sessions_by_regex_fallback(content)

        if sessions:
            self.log(f"✅ 总共提取到 {len(sessions)} 个场次")
        else:
            self.log("⚠️ 未提取到任何场次信息")

        return sessions

    def _extract_sessions_by_regex_fallback(self, content: str) -> List[Dict]:
        """正则表达式备选方案（兼容性保证）"""
        sessions = []
        try:
            # 原有的正则表达式逻辑作为备选
            perform_order_pattern = r'<div[^>]*class="[^"]*perform__order__select[^"]*perform__order__select__performs[^"]*"[^>]*>(.*?)</div>\s*</div>\s*</div>'
            perform_match = re.search(
                perform_order_pattern, content, re.IGNORECASE | re.DOTALL
            )

            if perform_match:
                perform_content = perform_match.group(1)
                self.log("🔍 正则表达式备选：找到perform容器")

                session_items = re.findall(
                    r'<div[^>]*class="[^"]*select_right_list_item[^"]*"[^>]*>(.*?)</div>',
                    perform_content,
                    re.IGNORECASE | re.DOTALL,
                )

                for i, item in enumerate(session_items):
                    time_match = re.search(
                        r"(\d{4}-\d{2}-\d{2})\s*([星期\u4e00-\u9fa5]+)\s*(\d{1,2}:\d{2})",
                        item,
                    )
                    if time_match:
                        date_part = time_match.group(1)
                        weekday = time_match.group(2)
                        time_part = time_match.group(3)
                        session_time = f"{date_part} {weekday} {time_part}"

                        sessions.append(
                            {
                                "id": f"regex_session_{i}",
                                "name": session_time,
                                "session_time": session_time,
                                "date": date_part,
                                "weekday": weekday,
                                "time": time_part,
                                "date_display": session_time,
                                "has_discount": "惠" in item,
                                "status": "active" if "active" in item else "available",
                            }
                        )

                self.log(f"✅ 正则表达式备选：提取到 {len(sessions)} 个场次")

        except Exception as e:
            self.log(f"❌ 正则表达式备选方案也失败: {e}")

        return sessions

    def extract_show_description_by_detail_class(self, content: str) -> Dict:
        """基于BeautifulSoup精准定位剧目介绍信息"""
        description_info = {
            "show_description": "",
            "images": [],
            "performance_info": {},
            "cast_info": [],
            "notices": [],
        }

        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(content, "html.parser")

            # 方法1: 精确定位 id="detail" class="list" 的div
            detail_div = soup.find("div", {"id": "detail", "class": "list"})

            if not detail_div:
                # 方法2: 只根据id定位
                detail_div = soup.find("div", {"id": "detail"})

            if not detail_div:
                # 方法3: 查找包含"剧目介绍"的title div
                title_divs = soup.find_all("div", class_="title", string="剧目介绍")
                if title_divs:
                    detail_div = title_divs[0].parent

            if detail_div:
                self.log("🔍 找到剧目介绍容器")

                # 精确查找words div
                words_div = detail_div.find("div", class_="words")

                if words_div:
                    self.log(f"   📄 找到words容器")

                    # 提取图片链接
                    img_tags = words_div.find_all("img")
                    cleaned_images = []
                    for img in img_tags:
                        src = img.get("src")
                        if src:
                            # 添加协议
                            if src.startswith("//"):
                                src = "https:" + src
                            elif not src.startswith("http"):
                                src = "https://" + src
                            # 移除质量和格式参数
                            cleaned_src = re.sub(r"_q\d+\.jpg_\.webp$", "", src)
                            cleaned_src = re.sub(r"\.webp$", "", cleaned_src)
                            if cleaned_src not in cleaned_images:
                                cleaned_images.append(cleaned_src)
                    description_info["images"] = cleaned_images
                    self.log(f"   🖼️ 提取图片: {len(description_info['images'])} 张")

                    # 获取纯文本内容，采用多层策略
                    description_text = ""

                    # 策略1: 寻找包含实际文本内容的p标签
                    all_p_tags = words_div.find_all("p")
                    valid_text_parts = []

                    for p in all_p_tags:
                        # 获取纯文本，排除img标签
                        text_content = ""
                        for elem in p.contents:
                            if isinstance(elem, str):
                                text_content += elem.strip()
                            elif hasattr(elem, "name") and elem.name not in [
                                "img",
                                "script",
                                "style",
                            ]:
                                text_content += elem.get_text().strip()

                        text_content = text_content.strip()

                        # 跳过空文本或过短文本
                        if not text_content or len(text_content) < 5:
                            continue

                        # 跳过只包含特殊字符的文本
                        if not any(c.isalnum() for c in text_content):
                            continue

                        # 跳过明显的须知、提示等无关内容（但标准稍微放松）
                        if any(
                            keyword in text_content
                            for keyword in [
                                "购票须知",
                                "观演须知",
                                "退票规则",
                                "入场时间",
                                "儿童票规则",
                                "发票须知",
                                "限购规则",
                                "谢绝拍照",
                                "演出票一经售出恕不",
                                "凭票入场",
                                "食品饮料谢绝",
                                "食品、饮料、水、鲜花、相机、摄像机谢绝带入",
                                "演出票具有唯一性、时效性",
                            ]
                        ):
                            continue

                        # 收集有效文本
                        valid_text_parts.append(text_content)
                        self.log(f"   📝 发现文本段落: {text_content[:30]}...")

                    # 策略2: 如果words中没有文本，查看是否有其他类型的内容
                    if not valid_text_parts:
                        # 检查是否只包含图片
                        img_count = len(words_div.find_all("img"))
                        if img_count > 0:
                            self.log(
                                f"   🖼️ words容器只包含 {img_count} 张图片，无文本描述"
                            )
                        else:
                            # 获取所有文本内容（包括可能被过滤的）
                            all_text = words_div.get_text().strip()
                            if all_text and len(all_text) > 10:
                                # 清理文本，移除多余空白
                                all_text = " ".join(all_text.split())
                                valid_text_parts.append(all_text)
                                self.log(f"   📄 使用容器所有文本: {all_text[:50]}...")

                    # 策略3: 组合文本
                    if valid_text_parts:
                        # 取前3个段落，但限制总长度
                        combined_text = " ".join(valid_text_parts[:3])
                        # 移除多余的空白字符
                        combined_text = " ".join(combined_text.split())
                        description_info["show_description"] = combined_text[:1000]
                        self.log(f"   📄 提取描述: {combined_text[:50]}...")
                        self.log(f"   📄 段落数: {len(valid_text_parts[:3])}")
                    else:
                        self.log(f"   ⚠️ words容器中未找到可用的文本内容")

                    self.log(
                        f"   📄 最终描述: {description_info['show_description'][:100]}..."
                    )
                    self.log(f"   🖼️ 图片数量: {len(description_info['images'])} 张")

                else:
                    self.log("⚠️ 未找到words容器，使用整个detail容器")
                    # 如果没有找到words div，使用整个detail div的文本
                    text = detail_div.get_text().strip()
                    # 清理文本，移除多余空白
                    text = re.sub(r"\s+", " ", text)
                    if text and len(text) > 10:
                        description_info["show_description"] = text[:1000]
                        self.log(f"   📄 备选描述长度: {len(text)} 字符")

            else:
                self.log("❌ 未找到剧目介绍容器 (id='detail')")

        except Exception as e:
            self.log(f"❌ BeautifulSoup提取剧目介绍失败: {e}")
            # fallback 到正则表达式方法
            try:
                detail_pattern = r'<div[^>]*id="detail"[^>]*class="[^"]*list[^"]*"[^>]*>.*?<div[^>]*class="[^"]*words[^"]*"[^>]*>(.*?)</div>'
                detail_match = re.search(
                    detail_pattern, content, re.IGNORECASE | re.DOTALL
                )
                if detail_match:
                    words_content = detail_match.group(1)
                    clean_text = re.sub(r"<[^>]+>", "", words_content)
                    clean_text = re.sub(r"\s+", " ", clean_text).strip()
                    if clean_text and len(clean_text) > 10:
                        description_info["show_description"] = clean_text[:1000]
                        self.log(f"   📄 正则fallback成功: {len(clean_text)} 字符")
            except Exception as fallback_e:
                self.log(f"❌ 正则fallback也失败: {fallback_e}")

        return description_info

    def crawl_detail_page_with_playwright(self, item: Dict[str, str]) -> bool:
        """使用 Playwright 爬取详情页"""
        item_id = item["id"]
        detail_url = item["url"]
        try:
            self.log(f"🔍 [Playwright] 爬取详情页: {item_id}")
            self.page.goto(detail_url, wait_until="networkidle", timeout=30000)
            time.sleep(2)

            content = self.page.content()  # 获取 HTML
            detail_info = self.extract_detail_info(content, item_id, detail_url)
            self.details_data.append(detail_info)
            self.crawled_ids.add(item_id)
            self.update_status(
                current_item=f"已保存: {item_id}",
                message=f"已保存详情，当前总数: {len(self.details_data)}",
            )

            # 保存文件
            detail_file = os.path.join(self.output_dir, f"detail_{item_id}.md")
            with open(detail_file, "w", encoding="utf-8") as f:
                f.write(content)

            self._save_details_data()
            self._save_crawled_ids()
            self.log(f"✅ [Playwright] 详情页爬取成功: {item_id}")
            return True
        except Exception as e:
            self.log(f"❌ [Playwright] 详情页爬取失败: {item_id}, {e}")
            return False

    def run_deep_crawl(
        self,
        max_items: int = 50,
        max_pages: int = 10,
        delay_between_requests: float = 2.0,
        headless: bool = False,
    ):
        """运行深度爬取 - 使用Playwright可视化点击分页和详情页"""

        # 保存参数为实例变量
        self.delay_between_requests = delay_between_requests
        self.max_items = max_items
        self.max_pages = max_pages

        # 添加兼容性检查
        if not self.check_playwright_compatibility():
            self.log("❌ Playwright兼容性检查失败")
            return

        self.log(
            f"🚀 开始深度爬取，最大项目数: {max_items}, 最大页数: {max_pages}, 请求间隔: {delay_between_requests}秒"
        )

        # 设置Playwright浏览器
        if not self.setup_playwright_browser(headless=headless):
            self.log("❌ 无法启动浏览器，停止爬取")
            return

        try:
            # 步骤1：使用Playwright爬取列表页
            self.log("🔄 开始使用Playwright爬取列表页...")
            self.update_status(message="正在爬取列表页...")
            items = self.crawl_multiple_pages_with_playwright(max_pages)
            if not items:
                self.log("❌ 未能获取到任何记录项")
                self.update_status(message="未能获取到任何记录项")
                return
            self.log(f"📋 共发现 {len(items)} 个记录项")

            # 过滤出需要爬取的新项目
            total_items = len(items)
            new_items = [item for item in items if item["id"] not in self.crawled_ids]
            new_items_count = len(new_items)

            self.log(
                f"📊 项目统计：发现 {total_items} 个项目，其中 {new_items_count} 个新项目，{total_items - new_items_count} 个已爬取"
            )

            # 限制新项目数量不超过max_items
            if new_items_count > max_items:
                new_items = new_items[:max_items]
                new_items_count = max_items
                self.log(f"🔄 限制本次爬取数量为 {max_items} 个新项目")

            # 设置进度条：总数为max_items，当前进度为新爬取的数量
            self.update_status(
                progress=0,
                total=max_items,
                message=f"发现 {new_items_count} 个新项目，开始爬取...",
            )

            # 步骤2：用 Playwright 爬取详情页
            self.log("🔄 开始爬取详情页...")
            crawled_count = 0
            skipped_count = 0

            for i, item in enumerate(items):
                if self.is_stopped():
                    self.log("🛑 检测到停止信号，停止爬取")
                    break
                if crawled_count >= max_items:
                    self.log(f"✅ 已达到最大爬取数量限制: {max_items}")
                    break

                item_id = item["id"]

                if item_id in self.crawled_ids:
                    self.log(f"⏭️  跳过已爬取项目: {item_id}")
                    skipped_count += 1
                    continue

                self.log(f"\n📄 处理新项目 {crawled_count + 1}/{max_items}: {item_id}")
                # 更新当前处理的项目，进度为已爬取的数量
                self.update_status(
                    progress=crawled_count,
                    total=max_items,
                    current_item=f"正在爬取: {item_id}",
                )

                success = self.crawl_detail_page_with_playwright(item)
                if success:
                    crawled_count += 1
                    self.update_status(
                        progress=crawled_count,
                        total=max_items,
                        current_item=f"完成: {item_id}",
                    )
                    self.log(f"✅ 成功爬取: {crawled_count}/{max_items}")
                else:
                    self.log(f"❌ 爬取失败: {item_id}")
                    self.update_status(current_item=f"失败: {item_id}")

                if delay_between_requests > 0:
                    self.log(f"⏳ 等待 {delay_between_requests} 秒...")
                    time.sleep(delay_between_requests)

            # 最终统计
            self.log(f"\n🎉 深度爬取完成!")
            self.log(f"📊 统计信息:")
            self.log(f"   - 发现记录项: {len(items)}")
            self.log(f"   - 最大爬取项目: {max_items}")
            self.log(f"   - 新爬取项目: {crawled_count}")
            self.log(f"   - 跳过项目: {skipped_count}")
            self.log(f"   - 总爬取项目: {len(self.crawled_ids)}")
            self.log(f"   - 详情数据: {len(self.details_data)} 条")

            # 最终状态更新
            self.update_status(
                progress=crawled_count,
                total=max_items,
                current_item="爬取完成",
                message=f"完成！新爬取 {crawled_count} 个，跳过 {skipped_count} 个，总计 {len(self.details_data)} 个",
            )

            # 生成汇总报告
            self.generate_summary_report()
            self.log("✅ 爬取任务完成，数据已保存到本地文件")

        except Exception as e:
            self.log(f"❌ 深度爬取异常: {e}")
        finally:
            self.close_playwright_browser()

    def generate_summary_report(self):
        """生成汇总报告"""
        report_file = os.path.join(self.output_dir, "crawl_summary.json")

        summary = {
            "crawl_time": datetime.now().isoformat(),
            "base_url": self.base_url,
            "total_crawled": len(self.crawled_ids),
            "total_details": len(self.details_data),
            "crawled_ids": list(self.crawled_ids),
            "sample_details": self.details_data[:5] if self.details_data else [],
            "statistics": {
                "avg_content_length": sum(
                    item.get("raw_content_length", 0) for item in self.details_data
                )
                / len(self.details_data)
                if self.details_data
                else 0,
                "items_with_title": sum(
                    1 for item in self.details_data if item.get("title") != "待解析"
                ),
                "items_with_venue": sum(
                    1 for item in self.details_data if item.get("venue") != "待解析"
                ),
                "items_with_time": sum(
                    1
                    for item in self.details_data
                    if item.get("time_period") != "待解析"
                ),
                "items_with_poster": sum(
                    1 for item in self.details_data if item.get("poster")
                ),
            },
        }

        try:
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            self.log(f"📊 汇总报告已生成: {report_file}")
        except Exception as e:
            self.log(f"⚠️  生成汇总报告失败: {e}")

    def extract_basic_time_info(self, content: str) -> Dict:
        """提取基本的时间信息（当没有详细场次时使用）"""
        basic_info = {
            "id": "basic_time",
            "name": "演出时间",
            "session_time": "",
            "date": "",
            "time": "",
            "date_display": "",
            "city": "默认城市",
        }

        try:
            # 从class="time"的div中提取时间信息
            time_patterns = [
                r'<div[^>]*class="[^"]*time[^"]*"[^>]*>时间：([^<]+)</div>',
                r'<div[^>]*class="[^"]*address[^"]*"[^>]*>.*?<div[^>]*class="[^"]*time[^"]*"[^>]*>时间：([^<]+)</div>',
                r"时间：(\d{4}\.\d{1,2}\.\d{1,2}[-~]\d{1,2}\.\d{1,2})",
            ]

            for pattern in time_patterns:
                match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
                if match:
                    time_str = match.group(1).strip()
                    basic_info["session_time"] = time_str
                    basic_info["name"] = time_str
                    basic_info["date_display"] = time_str
                    basic_info["date"] = time_str
                    self.log(f"✅ 提取基本时间信息: {time_str}")
                    break

        except Exception as e:
            self.log(f"❌ 提取基本时间信息失败: {e}")

        return basic_info

    def check_playwright_compatibility(self):
        """检查Playwright版本兼容性"""
        try:
            import playwright

            version = playwright.__version__
            self.log(f"🔍 Playwright版本: {version}")

            # 检查版本是否支持所需功能
            from packaging import version as pkg_version

            if pkg_version.parse(version) < pkg_version.parse("1.20.0"):
                self.log("⚠️ Playwright版本可能过低，建议升级到1.20.0以上")
                return False
            return True
        except Exception as e:
            self.log(f"⚠️ 无法检查Playwright版本: {e}")
            return True  # 默认继续执行

    def _extract_sessions_from_date_buttons(self) -> List[Dict]:
        """从日期按钮中提取场次信息"""
        sessions = []

        try:
            # 查找日期按钮的备选选择器
            button_selectors = [
                'button[class*="date"]',
                ".date-btn",
                ".perform-date-btn",
                "[data-date]",
                '.tab-item[data-type="date"]',
            ]

            for selector in button_selectors:
                buttons = self.page.query_selector_all(selector)
                if buttons:
                    self.log(f"✅ 找到日期按钮: {selector}")

                    for i, button in enumerate(buttons):
                        if button.is_visible() and button.is_enabled():
                            try:
                                date_text = button.inner_text().strip()
                                self.log(f"🔄 点击日期按钮 {i+1}: {date_text}")

                                button.click()
                                time.sleep(2)

                                self._wait_for_sessions_to_load()
                                current_sessions = (
                                    self._extract_sessions_for_current_date(date_text)
                                )
                                sessions.extend(current_sessions)

                            except Exception as e:
                                self.log(f"❌ 点击日期按钮失败: {e}")
                    break

        except Exception as e:
            self.log(f"❌ 从日期按钮提取场次失败: {e}")

        return sessions

    def _extract_default_sessions(self) -> List[Dict]:
        """默认的场次提取方法（降级处理）"""
        try:
            content = self.page.content()
            return self.extract_sessions_by_perform_order_class(content)
        except Exception as e:
            self.log(f"❌ 默认场次提取失败: {e}")
            return []

    def _deduplicate_sessions(self, sessions: List[Dict]) -> List[Dict]:
        """去重处理场次信息"""
        unique_sessions = []
        seen_sessions = set()

        for session in sessions:
            # 创建唯一标识
            session_key = f"{session.get('date', '')}_{session.get('time', '')}_{session.get('session_time', '')}"

            if session_key not in seen_sessions:
                seen_sessions.add(session_key)
                unique_sessions.append(session)

        return unique_sessions

    def _get_current_month_info(self) -> str:
        """获取当前显示的月份信息"""
        try:
            # 常见的月份显示选择器
            month_selectors = [
                ".calendar-header .month",
                ".month-year",
                ".calendar-month",
                '[class*="month"]',
                ".calendar-title",
                ".date-header",
            ]

            for selector in month_selectors:
                try:
                    element = self.page.query_selector(selector)
                    if element and element.is_visible():
                        month_text = element.inner_text().strip()
                        if month_text:
                            self.log(f"📅 检测到当前月份: {month_text}")
                            return month_text
                except:
                    continue

            # 如果没有找到专门的月份显示，尝试从页面内容中提取
            content = self.page.content()
            import re

            month_patterns = [
                r"(\d{4}年\d{1,2}月)",
                r"(\d{4}-\d{1,2})",
                r"(\d{1,2}月\s*\d{4})",
            ]

            for pattern in month_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    return matches[0]

        except Exception as e:
            self.log(f"⚠️ 获取当前月份信息失败: {e}")

        return None

    def _extract_sessions_from_current_month(self) -> List[Dict]:
        """提取当前月份的场次信息"""
        try:
            # 等待页面稳定
            time.sleep(2)

            # 查找当前月份所有可点击的日期
            date_elements = self._find_clickable_dates()

            if not date_elements:
                self.log("⚠️ 当前月份未找到可点击日期")
                return []

            sessions = []
            visited_dates = set()

            for i, date_element in enumerate(date_elements):
                try:
                    date_text = date_element.inner_text().strip()

                    # 避免重复点击
                    if date_text in visited_dates:
                        continue
                    visited_dates.add(date_text)

                    self.log(
                        f"🔄 点击当前月份日期 {i+1}/{len(date_elements)}: {date_text}"
                    )

                    # 点击日期
                    date_element.click()
                    time.sleep(2)

                    # 等待场次加载
                    self._wait_for_sessions_to_load()

                    # 提取场次
                    current_sessions = self._extract_sessions_for_current_date(
                        date_text
                    )
                    if current_sessions:
                        sessions.extend(current_sessions)
                        self.log(
                            f"✅ 日期 {date_text} 获取到 {len(current_sessions)} 个场次"
                        )

                    time.sleep(1)  # 间隔

                except Exception as e:
                    self.log(f"❌ 点击日期失败: {e}")
                    continue

            return sessions

        except Exception as e:
            self.log(f"❌ 提取当前月份场次失败: {e}")
            return []

    def _find_clickable_dates(self) -> List:
        """查找所有可点击的日期元素"""
        date_selectors = [
            ".calendar-day:not(.disabled)",
            ".date-item:not(.disabled)",
            ".perform-date:not(.disabled)",
            ".calendar-date:not(.disabled)",
            '[class*="date"][class*="item"]:not(.disabled)',
            '[class*="calendar"][class*="day"]:not(.disabled)',
            'button[class*="date"]:not([disabled])',
            'span[class*="date"]:not(.disabled)',
            'div[class*="date"]:not(.disabled)',
        ]

        date_elements = []
        for selector in date_selectors:
            try:
                elements = self.page.query_selector_all(selector)
                for element in elements:
                    if element.is_visible() and element.is_enabled():
                        text = element.inner_text().strip()
                        if text and (text.isdigit() or "月" in text or "日" in text):
                            date_elements.append(element)
            except:
                continue

        return date_elements

    def _navigate_and_extract_months(
        self, direction: str, max_months: int, visited_months: set
    ) -> List[Dict]:
        """
        导航并提取指定方向的月份场次

        Args:
            direction: 'next' 或 'prev'
            max_months: 最大翻页数
            visited_months: 已访问的月份集合
        """
        sessions = []
        navigation_count = 0

        while navigation_count < max_months:
            try:
                # 尝试翻页
                if not self._navigate_month(direction):
                    self.log(f"⚠️ {direction} 方向无法继续翻页")
                    break

                navigation_count += 1

                # 等待页面更新
                time.sleep(2)

                # 获取新月份信息
                current_month = self._get_current_month_info()
                if current_month in visited_months:
                    self.log(f"⚠️ 月份 {current_month} 已访问过，停止翻页")
                    break

                visited_months.add(current_month)
                self.log(f"📅 正在处理月份: {current_month}")

                # 提取当前月份的场次
                month_sessions = self._extract_sessions_from_current_month()
                if month_sessions:
                    sessions.extend(month_sessions)
                    self.log(
                        f"✅ 月份 {current_month} 获取到 {len(month_sessions)} 个场次"
                    )
                else:
                    self.log(f"⚠️ 月份 {current_month} 没有场次信息")

            except Exception as e:
                self.log(f"❌ 翻页处理失败: {e}")
                break

        self.log(f"📊 {direction} 方向翻页完成，共翻了 {navigation_count} 个月")
        return sessions

    def _navigate_month(self, direction: str) -> bool:
        """
        翻页到上个月或下个月

        Args:
            direction: 'next' 或 'prev'

        Returns:
            bool: 是否成功翻页
        """
        try:
            # 翻月按钮的选择器
            if direction == "next":
                button_selectors = [
                    ".calendar-next",
                    ".next-month",
                    ".month-next",
                    ".calendar-header .next",
                    '[class*="next"]',
                    ".calendar-arrow.right",
                    'button[class*="next"]',
                    ".calendar-navigation .next",
                ]
            else:  # prev
                button_selectors = [
                    ".calendar-prev",
                    ".prev-month",
                    ".month-prev",
                    ".calendar-header .prev",
                    '[class*="prev"]',
                    ".calendar-arrow.left",
                    'button[class*="prev"]',
                    ".calendar-navigation .prev",
                ]

            for selector in button_selectors:
                try:
                    button = self.page.query_selector(selector)
                    if button and button.is_visible() and button.is_enabled():
                        # 检查按钮是否被禁用
                        classes = button.get_attribute("class") or ""
                        if "disabled" not in classes.lower():
                            self.log(f"🔄 点击 {direction} 翻页按钮: {selector}")
                            button.click()
                            time.sleep(2)  # 等待翻页动画
                            return True
                except Exception as e:
                    self.log(f"⚠️ 尝试翻页按钮失败: {selector} - {e}")
                    continue

            self.log(f"❌ 未找到可用的 {direction} 翻页按钮")
            return False

        except Exception as e:
            self.log(f"❌ 翻页操作失败: {e}")
            return False

    def _navigate_to_start_month(self, start_month: str):
        """导航回到起始月份"""
        if not start_month:
            return

        try:
            # 尝试回到起始月份（最多尝试10次）
            for attempt in range(10):
                current_month = self._get_current_month_info()
                if current_month == start_month:
                    self.log(f"✅ 已回到起始月份: {start_month}")
                    return

                # 尝试向前翻页回到起始月份
                if not self._navigate_month("prev"):
                    break

                time.sleep(1)

            self.log(f"⚠️ 无法回到起始月份: {start_month}")

        except Exception as e:
            self.log(f"❌ 导航回起始月份失败: {e}")

    def _detect_calendar_presence(self) -> Dict[str, Any]:
        """快速检测页面是否存在日历组件并返回检测结果"""
        detection_result = {
            "has_calendar": False,
            "calendar_type": None,
            "container": None,
            "selector_used": None,
        }

        if not self.page:
            return detection_result

        # 按优先级排序的日历选择器
        calendar_selectors = [
            (".functional-calendar", "advanced"),  # 高级多月日历
            (".perform__order__select__calendar", "standard"),  # 大麦网标准日历
            (".calendar-for", "simple"),  # 简单日历
            (".wh_content_item", "date_grid"),  # 日期网格
            ('[class*="calendar"]', "generic"),  # 通用日历
            (".perform__order__calendar", "basic"),  # 基础日历
            (".calendar-wrapper", "wrapper"),  # 日历包装器
        ]

        self.log("🔍 快速检测页面日历组件...")

        for selector, calendar_type in calendar_selectors:
            try:
                # 快速检测，不等待太久
                elements = self.page.query_selector_all(selector)
                if elements:
                    # 检查是否有可见的元素
                    for element in elements:
                        if element.is_visible():
                            detection_result.update(
                                {
                                    "has_calendar": True,
                                    "calendar_type": calendar_type,
                                    "container": element,
                                    "selector_used": selector,
                                }
                            )
                            self.log(
                                f"✅ 检测到日历组件: {selector} (类型: {calendar_type})"
                            )
                            return detection_result
            except Exception as e:
                continue

        self.log("ℹ️ 未检测到日历组件，将使用标准HTML解析")
        return detection_result

    def _find_calendar_container_optimized(self):
        """基于分析结果的优化日历容器查找"""
        # 基于分析结果的精确选择器
        optimized_selectors = [
            ".functional-calendar",  # 分析中发现的主要日历
            ".perform__order__select__calendar",  # 大麦网特有
            ".calendar-for",  # 分析中发现的另一个日历
            '[class*="calendar"]',  # 备选
        ]

        for selector in optimized_selectors:
            try:
                container = self.page.wait_for_selector(selector, timeout=3000)
                if container and container.is_visible():
                    self.log(f"✅ 找到优化日历容器: {selector}")
                    return container
            except:
                continue

                return None

    def _smart_calendar_extraction(
        self, url: str, item_id: str, detection_result: Dict[str, Any]
    ) -> List[Dict]:
        """基于检测结果的智能日历提取"""
        if not detection_result["has_calendar"]:
            self.log("📋 无日历组件，跳过日历解析")
            return []

        calendar_type = detection_result["calendar_type"]
        selector = detection_result["selector_used"]

        self.log(f"🎯 开始智能日历提取 (类型: {calendar_type}, 选择器: {selector})")

        try:
            # 根据日历类型选择最适合的提取模式
            if calendar_type in ["advanced", "standard"]:
                # 高级或标准日历，优先使用多月优化模式
                self.log("🚀 使用多月优化模式提取...")
                sessions = self.extract_sessions_with_calendar_unified(
                    url, item_id, mode="optimized", max_months=3
                )

                if sessions:
                    self.log(f"✅ 多月优化模式成功获取 {len(sessions)} 个场次")
                    return sessions
                else:
                    # 如果优化模式失败，降级到单月模式
                    self.log("⚠️ 多月模式无结果，降级到单月模式...")
                    sessions = self.extract_sessions_with_calendar_unified(
                        url, item_id, mode="single"
                    )
                    if sessions:
                        self.log(f"✅ 单月模式成功获取 {len(sessions)} 个场次")
                    return sessions

            elif calendar_type in ["simple", "date_grid", "basic"]:
                # 简单日历，直接使用单月模式
                self.log("📅 使用单月模式提取...")
                sessions = self.extract_sessions_with_calendar_unified(
                    url, item_id, mode="single"
                )
                if sessions:
                    self.log(f"✅ 单月模式成功获取 {len(sessions)} 个场次")
                return sessions

            else:
                # 通用日历，尝试优化模式，失败则降级
                self.log("🔄 通用日历，尝试优化模式...")
                sessions = self.extract_sessions_with_calendar_unified(
                    url, item_id, mode="optimized", max_months=2
                )

                if not sessions:
                    self.log("⚠️ 优化模式无结果，尝试单月模式...")
                    sessions = self.extract_sessions_with_calendar_unified(
                        url, item_id, mode="single"
                    )

                if sessions:
                    self.log(f"✅ 日历提取成功获取 {len(sessions)} 个场次")
                return sessions

        except Exception as e:
            self.log(f"❌ 智能日历提取失败: {e}")
            return []

        return []

    def _extract_all_visible_sessions_optimized(self) -> List[Dict]:
        """优化版提取当前页面所有可见场次 - 只点击有效日期，跳过灰色日期"""
        sessions = []

        try:
            self.log("🔄 使用最终正确特征精确提取有效日期...")

            # 获取所有日期容器元素（wh_content_item）
            all_content_items = self.page.query_selector_all(".wh_content_item")
            self.log(f"🔍 总共发现 {len(all_content_items)} 个日期容器")

            valid_dates = {}
            disabled_count = 0
            invalid_count = 0

            # 对all_content_items掐头去尾，只保留当前月份的日期
            # 建立容器和日期的配对关系
            container_date_pairs = []
            for container in all_content_items:
                try:
                    date_element = container.query_selector(".wh_item_date")
                    if date_element:
                        date_text = date_element.inner_text().strip()
                        if date_text and date_text.isdigit():
                            container_date_pairs.append((container, date_text))
                except:
                    continue

            # 提取日期文本用于查找1号
            date_texts = [date_text for _, date_text in container_date_pairs]

            # 找到第一个"1号"的索引
            try:
                first_one_idx = date_texts.index("1")
            except ValueError:
                first_one_idx = 0  # 没有1号，默认不掐头

            # 找到第二个"1号"的索引
            try:
                second_one_idx = date_texts.index("1", first_one_idx + 1)
            except ValueError:
                second_one_idx = len(date_texts)  # 没有第二个1号，保留到末尾

            # 对配对列表进行切片，只保留第一个1号到第二个1号之间的日期
            container_date_pairs = container_date_pairs[first_one_idx:second_one_idx]

            # 提取处理后的容器列表
            all_content_items = [container for container, _ in container_date_pairs]
            processed_dates = [date_text for _, date_text in container_date_pairs]

            self.log(f"📅 掐头去尾后保留 {len(all_content_items)} 个日期容器")
            self.log(
                f"📅 处理后的日期范围: {processed_dates[0] if processed_dates else '无'} 到 {processed_dates[-1] if processed_dates else '无'}"
            )

            for container in all_content_items:
                try:
                    if not container.is_visible():
                        invalid_count += 1
                        continue

                    # 检查容器是否有disabled类名
                    container_classes = container.get_attribute("class") or ""
                    is_disabled = "disabled" in container_classes

                    # 获取容器内的日期元素
                    date_element = container.query_selector(".wh_item_date")

                    if not date_element:
                        invalid_count += 1
                        continue

                    date_text = date_element.inner_text().strip()
                    if not date_text or not date_text.isdigit():
                        invalid_count += 1
                        continue

                    date_classes = date_element.get_attribute("class") or ""
                    is_clickable = "cursor-pointer" in date_classes

                    if is_disabled:
                        disabled_count += 1
                        self.log(
                            f"⏭️ 跳过灰色日期（disabled）{date_text} (容器类名: {container_classes})"
                        )
                        continue

                    if not is_clickable:
                        invalid_count += 1
                        self.log(f"⏭️ 跳过不可点击日期 {date_text}")
                        continue

                    # 这是真正的高亮日期（有场次的日期）
                    if date_text not in valid_dates:
                        valid_dates[date_text] = date_element
                        self.log(
                            f"✅ 发现高亮日期（有场次）: {date_text} (容器类名: {container_classes})"
                        )

                except Exception as e:
                    invalid_count += 1
                    continue

            self.log(f"📊 智能筛选统计:")
            self.log(f"   ✅ 高亮日期（有场次）: {len(valid_dates)} 个")
            self.log(f"   ⏭️ 灰色日期（disabled）: {disabled_count} 个")
            self.log(f"   ❌ 其他无效日期: {invalid_count} 个")
            self.log(
                f"   🚀 性能提升: 跳过 {disabled_count + invalid_count} 次无效点击"
            )
            self.log(f"   🎯 关键特征: 外层容器'wh_content_item'包含'disabled'类名")

            if not valid_dates:
                self.log("⚠️ 未找到任何有效日期")
                return sessions

            # 只点击有效的日期
            for i, (date_text, element) in enumerate(valid_dates.items()):
                try:
                    self.log(f"🎯 点击有效日期 {i+1}/{len(valid_dates)}: {date_text}")

                    # 再次确认元素仍然有效
                    if not element.is_visible() or not element.is_enabled():
                        self.log(f"⚠️ 日期 {date_text} 状态已变化，跳过")
                        continue

                    # 点击日期
                    element.click()
                    time.sleep(0.8)

                    # 等待场次加载
                    self._wait_for_sessions_to_load()

                    # 快速提取场次
                    current_sessions = self._extract_sessions_for_current_date(
                        date_text
                    )
                    if current_sessions:
                        sessions.extend(current_sessions)
                        self.log(
                            f"✅ 日期 {date_text} 获取到 {len(current_sessions)} 个场次"
                        )
                    else:
                        self.log(f"💡 日期 {date_text} 无场次安排")

                    # 极短间隔
                    time.sleep(0.3)

                except Exception as e:
                    self.log(f"❌ 点击日期 {date_text} 失败: {e}")
                    continue

            self.log(f"🎉 智能优化完成，共获得 {len(sessions)} 个场次")
            return sessions

        except Exception as e:
            self.log(f"❌ 智能提取失败: {e}")
            return []

    def _wait_for_sessions_to_load(self):
        """等待场次信息加载（优化版）"""
        try:
            # 快速检测场次元素是否出现
            session_selectors = [
                ".perform__order__time",
                ".perform__order__select__performs",
                '[class*="session"]',
            ]

            for selector in session_selectors:
                try:
                    self.page.wait_for_selector(selector, timeout=1500)  # 减少到1.5秒
                    break
                except:
                    continue

        except Exception as e:
            # 忽略等待超时，继续处理
            pass

    def _extract_sessions_for_current_date(self, date_text: str) -> List[Dict]:
        """提取当前日期的场次信息（优化版）"""
        try:
            # 获取当前页面内容
            content = self.page.content()

            # 使用现有的高效场次提取方法
            extracted_sessions = self.extract_sessions_by_perform_order_class(content)

            # 为每个场次添加日期信息
            sessions = []
            for session in extracted_sessions:
                session["calendar_date"] = date_text
                if not session.get("date") or session.get("date") == "默认日期":
                    session["date"] = self._format_date_from_calendar(date_text)
                sessions.append(session)

            return sessions

        except Exception as e:
            self.log(f"❌ 快速提取当前日期场次失败: {e}")
            return []

    def _format_date_from_calendar(self, date_text: str) -> str:
        """将日历日期格式化为标准日期格式"""
        try:
            from datetime import datetime

            # 获取当前年月
            current_date = datetime.now()
            current_year = current_date.year
            current_month = current_date.month

            # 简单格式化
            day = int(date_text)
            formatted_date = f"{current_year}-{current_month:02d}-{day:02d}"

            return formatted_date

        except:
            return date_text

    def _try_navigate_months_optimized(self, max_months: int) -> List[Dict]:
        """智能多月导航功能 - 避免重复处理"""
        all_sessions = []

        try:
            self.log(f"🗓️ 开始智能多月导航，最多 {max_months} 个月...")

            # 先处理当前月份（第一次访问）
            self.log("📅 提取当前月份场次...")
            current_month_sessions = self._extract_all_visible_sessions_optimized()

            if current_month_sessions:
                # 标记为当前月份
                for session in current_month_sessions:
                    session["source_month"] = "初始页面"
                    session["navigation_method"] = "initial_load"

                all_sessions.extend(current_month_sessions)
                self.log(f"✅ 当前月份获取到 {len(current_month_sessions)} 个场次")

            # 如果只需要1个月，直接返回
            if max_months <= 1:
                self.log("🔚 只需要1个月，直接返回当前月份结果")
                return all_sessions

            # 基于实际测试发现的翻月方式
            month_header_selector = ".wh_content.month"

            # 找到年月头部元素
            month_header = self.page.query_selector(month_header_selector)
            if not month_header or not month_header.is_visible():
                self.log("❌ 未找到月份头部元素，返回当前月份结果")
                return all_sessions

            self.log("✅ 找到月份头部元素，开始翻月...")

            # 点击月份头部
            month_header.click()
            time.sleep(1.5)

            # 查找出现的月份选择器
            month_selector = self.page.query_selector(".wh_top_changge")
            if not month_selector or not month_selector.is_visible():
                self.log("⚠️ 未找到月份选择器，返回当前月份结果")
                return all_sessions

            self.log("✅ 找到月份选择器")

            # 获取所有可选月份
            month_options = month_selector.query_selector_all("div, span")
            available_months = []

            for option in month_options:
                if option.is_visible():
                    option_text = option.inner_text().strip()
                    if "年" in option_text and "月" in option_text:
                        available_months.append(
                            {"text": option_text, "element": option}
                        )

            self.log(f"📅 发现 {len(available_months)} 个可选月份")

            # 🚨 从已提取的场次数据中识别真实月份，避免重复处理
            current_month_text = None
            try:
                # 从current_month_sessions中提取真实的月份信息
                if current_month_sessions:
                    # 获取第一个场次的日期，提取月份
                    first_session_date = current_month_sessions[0].get("date", "")
                    if first_session_date and "-" in first_session_date:
                        # 格式类似 "2025-08-01"
                        parts = first_session_date.split("-")
                        if len(parts) >= 2:
                            year = parts[0]
                            month = parts[1]
                            current_month_text = f"{year}年{month}月"
                            self.log(f"📅 从场次数据识别真实月份: {current_month_text}")

                # 如果从场次数据识别失败，直接跳过翻月（因为数据有问题）
                if not current_month_text:
                    self.log("⚠️ 无法从场次数据识别月份，为安全起见跳过翻月操作")
                    return all_sessions

            except Exception as e:
                self.log(f"⚠️ 月份识别异常: {e}，跳过翻月操作")
                return all_sessions

            # 标记当前月份已处理，避免重复提取
            processed_months = set()
            if current_month_text:
                processed_months.add(current_month_text)  # 标记当前月份已处理
                self.log(f"🔒 当前月份 {current_month_text} 已标记为已处理，避免重复")

            for month_option in available_months:
                month_text = month_option["text"]

                # 跳过已处理的月份（包括当前月份）
                if month_text in processed_months:
                    self.log(f"⏭️ 跳过已处理月份: {month_text}")
                    continue

                # 特别检查：如果这是当前系统月份，且我们已经提取过当前月份场次，则跳过
                if current_month_text and month_text == current_month_text:
                    self.log(
                        f"🔒 跳过重复的当前月份: {month_text} (已在初始加载时处理)"
                    )
                    processed_months.add(month_text)
                    continue

                self.log(f"🔄 尝试切换到新月份: {month_text}")

                try:
                    # 点击月份选项
                    month_option["element"].click()
                    time.sleep(2)

                    # 等待页面更新
                    self._wait_for_sessions_to_load()

                    # 提取新月份的场次
                    new_month_sessions = self._extract_all_visible_sessions_optimized()

                    if new_month_sessions:
                        # 检测是否与当前月份的场次重复（通过日期判断）
                        current_dates = set(
                            [s.get("date", "") for s in current_month_sessions]
                        )
                        new_dates = set([s.get("date", "") for s in new_month_sessions])

                        # 如果日期完全一样，说明没有真正切换月份，是重复数据
                        if current_dates == new_dates and len(current_dates) > 0:
                            self.log(
                                f"🔒 检测到 {month_text} 与当前月份数据完全相同，跳过重复数据"
                            )
                            self.log(f"   当前月份日期: {sorted(current_dates)}")
                            self.log(f"   新月份日期: {sorted(new_dates)}")
                            processed_months.add(month_text)
                            continue

                        # 添加月份标识
                        for session in new_month_sessions:
                            session["source_month"] = month_text
                            session["navigation_method"] = "header_click"

                        all_sessions.extend(new_month_sessions)
                        processed_months.add(month_text)
                        self.log(
                            f"✅ {month_text} 获取到 {len(new_month_sessions)} 个场次"
                        )
                        self.log(f"   新增日期: {sorted(new_dates - current_dates)}")
                    else:
                        self.log(f"💡 {month_text} 无场次安排")
                        processed_months.add(month_text)  # 即使无场次也标记为已处理

                    # 检查是否已达到最大月份数
                    if len(processed_months) >= max_months:  # 包含当前月份的总数
                        self.log(f"🔚 已处理 {len(processed_months)} 个月份，达到限制")
                        break

                    # 重新打开月份选择器准备下一次
                    month_header.click()
                    time.sleep(1)
                    month_selector = self.page.query_selector(".wh_top_changge")
                    if not month_selector:
                        break

                except Exception as e:
                    self.log(f"❌ 切换到 {month_text} 失败: {e}")
                    continue

            # 去重处理
            unique_sessions = self._deduplicate_sessions_optimized(all_sessions)
            self.log(f"🎉 多月导航完成，总共获取到 {len(unique_sessions)} 个唯一场次")
            return unique_sessions

        except Exception as e:
            self.log(f"❌ 多月导航失败: {e}")
            # 如果翻月失败，至少返回当前月份的结果
            return all_sessions

    def _deduplicate_sessions_optimized(self, sessions: List[Dict]) -> List[Dict]:
        """优化版去重处理"""
        if not sessions:
            return []

        unique_sessions = []
        seen_keys = set()

        for session in sessions:
            # 创建更精确的唯一标识
            session_key = f"{session.get('date', '')}_{session.get('time', '')}_{session.get('name', session.get('session_time', ''))}"

            if session_key not in seen_keys:
                seen_keys.add(session_key)
                unique_sessions.append(session)

        return unique_sessions

    def extract_sessions_with_calendar_unified(
        self, url: str, item_id: str, mode: str = "optimized", max_months: int = 3
    ) -> List[Dict]:
        """
        统一的日历场次提取方法 - 替代三个冗余方法

        Args:
            url: 大麦网详情页URL
            item_id: 演出项目ID
            mode: 提取模式
                - "single": 单月模式（只处理当前月份）
                - "multi": 多月模式（支持翻页，标准性能）
                - "optimized": 优化模式（支持翻页，高性能，默认）
            max_months: 最大翻页月份数（仅在multi和optimized模式下生效）

        Returns:
            List[Dict]: 所有场次信息列表
        """
        all_sessions = []

        try:
            self.log(f"🗓️ 使用统一日历方法({mode}模式)获取场次: {url}")

            # 访问页面
            self.page.goto(url, wait_until="networkidle", timeout=30000)
            self.log("✅ 页面加载完成")

            # 根据模式调整等待时间
            wait_time = 1.5 if mode == "optimized" else 3.0
            time.sleep(wait_time)

            # 查找日历容器 (统一使用优化版本)
            calendar_container = self._find_calendar_container_optimized()

            if not calendar_container:
                self.log("❌ 未找到日历容器，使用降级方法")
                return self._extract_default_sessions()

            # 根据模式执行不同的提取策略
            if mode == "single":
                # 单月模式：只处理当前可见的日期
                self.log("🔄 单月模式：提取当前月份场次")
                all_sessions = self._extract_sessions_from_current_month()

            elif mode == "multi":
                # 多月模式：标准性能的多月翻页
                self.log(f"🔄 多月模式：翻页提取最多{max_months}个月")
                all_sessions = self._extract_multi_month_sessions_standard(max_months)

            elif mode == "optimized":
                # 优化模式：高性能的多月翻页
                self.log(f"🚀 优化模式：高性能翻页提取最多{max_months}个月")
                all_sessions = self._extract_multi_month_sessions_optimized(max_months)

            # 统一去重处理
            unique_sessions = self._deduplicate_sessions_unified(all_sessions)

            self.log(
                f"🎉 统一日历方法({mode}模式)获取到 {len(unique_sessions)} 个唯一场次"
            )
            return unique_sessions

        except Exception as e:
            self.log(f"❌ 统一日历方法失败: {e}")
            # 降级到最基础的方法
            return self._extract_default_sessions()

    def _extract_multi_month_sessions_standard(self, max_months: int) -> List[Dict]:
        """标准性能的多月场次提取"""
        all_sessions = []
        visited_months = set()

        # 获取当前月份
        current_month = self._get_current_month_info()
        if current_month:
            visited_months.add(current_month)

        # 提取当前月份
        current_sessions = self._extract_sessions_from_current_month()
        if current_sessions:
            all_sessions.extend(current_sessions)
            self.log(f"✅ 当前月份获取到 {len(current_sessions)} 个场次")

        # 向后翻页
        future_sessions = self._navigate_and_extract_months(
            "next", max_months, visited_months
        )
        if future_sessions:
            all_sessions.extend(future_sessions)

        # 回到起始月份
        self._navigate_to_start_month(current_month)

        # 向前翻页
        past_sessions = self._navigate_and_extract_months(
            "prev", max_months, visited_months
        )
        if past_sessions:
            all_sessions.extend(past_sessions)

        return all_sessions

    def _extract_multi_month_sessions_optimized(self, max_months: int) -> List[Dict]:
        """优化性能的多月场次提取"""
        return self._try_navigate_months_optimized(max_months)

    def _deduplicate_sessions_unified(self, sessions: List[Dict]) -> List[Dict]:
        """统一的会话去重方法"""
        if not sessions:
            return []

        unique_sessions = []
        seen_keys = set()

        for session in sessions:
            # 创建唯一标识符
            session_key = f"{session.get('date', '')}-{session.get('time', '')}-{session.get('name', '')}"

            if session_key not in seen_keys:
                unique_sessions.append(session)
                seen_keys.add(session_key)

        return unique_sessions


if __name__ == "__main__":
    # 大麦网搜索URL
    url = "https://search.damai.cn/search.htm?spm=a2oeg.home.category.ditem_1.2f9423e1NcYicv&ctl=%E8%AF%9D%E5%89%A7%E6%AD%8C%E5%89%A7&order=1&cty=%E5%8C%97%E4%BA%AC"

    # 创建爬虫实例
    crawler = DeepCrawler(url)

    try:
        # 运行深度爬取
        crawler.run_deep_crawl(
            max_items=20,  # 最大爬取项目数
            max_pages=3,  # 最大页数
            delay_between_requests=2.0,  # 请求间隔
            headless=False,  # 是否使用无头模式
        )

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    finally:
        # 确保浏览器关闭
        crawler.close_playwright_browser()
