#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步服务端API客户端
用于与同步服务端进行通信
"""

import json
import logging
from typing import Dict, List, Optional

import requests
from config import SYNC_SERVER_CONFIG

logger = logging.getLogger(__name__)


class SyncServerClient:
    """同步服务端API客户端"""

    def __init__(self, base_url: str = None, api_key: str = None):
        """
        初始化客户端

        Args:
            base_url: 服务端API基础URL
            api_key: API密钥
        """
        self.base_url = (base_url or SYNC_SERVER_CONFIG["base_url"]).rstrip("/")
        self.api_key = api_key or SYNC_SERVER_CONFIG["api_key"]
        self.timeout = SYNC_SERVER_CONFIG["timeout"]
        self.retry_times = SYNC_SERVER_CONFIG["retry_times"]

        self.headers = {"X-API-Key": self.api_key, "Content-Type": "application/json"}

    def _request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """
        发送API请求

        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据

        Returns:
            Dict: API响应
        """
        url = f"{self.base_url}{endpoint}"

        for attempt in range(self.retry_times):
            try:
                if method.upper() == "GET":
                    response = requests.get(
                        url, headers=self.headers, timeout=self.timeout
                    )
                elif method.upper() == "POST":
                    response = requests.post(
                        url, headers=self.headers, json=data, timeout=self.timeout
                    )
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")

                response.raise_for_status()
                return response.json()

            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.retry_times}): {e}")
                if attempt == self.retry_times - 1:
                    return {
                        "success": False,
                        "error": str(e),
                        "message": f"请求失败: {e}",
                    }

    def check_server_health(self) -> Dict:
        """检查服务端健康状态"""
        try:
            # 健康检查不需要API密钥
            response = requests.get(
                f"{self.base_url.replace('/api/v1', '')}/health", timeout=self.timeout
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_server_info(self) -> Dict:
        """获取服务端信息"""
        try:
            # API信息接口不需要认证
            response = requests.get(f"{self.base_url}/info", timeout=self.timeout)
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}

    def upload_data(self, data: List[Dict]) -> Dict:
        """上传爬取数据到服务端"""
        payload = {"data": data}
        return self._request("POST", "/data/upload", payload)

    def get_data_list(self, include_sync_status: bool = True) -> Dict:
        """获取服务端数据列表"""
        endpoint = (
            f"/data?include_sync_status={'true' if include_sync_status else 'false'}"
        )
        return self._request("GET", endpoint)

    def get_sync_status(self) -> Dict:
        """获取同步状态"""
        return self._request("GET", "/sync/status")

    def get_sync_history(self, limit: int = 10, offset: int = 0) -> Dict:
        """获取同步历史"""
        endpoint = f"/sync/history?limit={limit}&offset={offset}"
        return self._request("GET", endpoint)

    def get_sync_statistics(self) -> Dict:
        """获取同步统计"""
        return self._request("GET", "/sync/statistics")

    def sync_data(self, selected_ids: List[str]) -> Dict:
        """执行数据同步"""
        payload = {"selected_ids": selected_ids}
        return self._request("POST", "/sync", payload)
