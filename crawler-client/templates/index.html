<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫管理界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 50% 50%;
            gap: 30px;
            padding: 30px;
            justify-content: center;
        }

        .config-panel,
        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            width: 100%;
            overflow: hidden;
        }

        .panel-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 0.9em;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(81, 207, 102, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #868e96 0%, #6c757d 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(134, 142, 150, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e1e5e9;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            transition: width 0.3s;
            position: relative;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: 600;
            font-size: 0.9em;
        }

        .status-info {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
            word-wrap: break-word;
            word-break: break-all;
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            word-wrap: break-word;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .log-entry {
            margin-bottom: 5px;
            opacity: 0.9;
            word-wrap: break-word;
            word-break: break-all;
        }

        /* 数据展示区域样式 */
        .data-section {
            grid-column: 1 / -1;
            margin-top: 20px;
        }

        .data-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            flex-wrap: wrap;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .selection-info {
            color: #666;
            font-size: 0.9em;
        }

        /* 过滤器样式 */
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 0.9em;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .pagination-info {
            color: #666;
            font-size: 0.9em;
            margin-right: 20px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s;
        }

        .pagination-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f8f9fa;
            color: #999;
        }

        /* 数据卡片样式 */
        .data-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            align-items: flex-start;
            gap: 20px;
            min-height: 200px;
        }

        .data-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .data-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }

        /* 海报区域 */
        .card-poster {
            flex-shrink: 0;
            width: 150px;
        }

        .poster-container {
            margin: 0;
            text-align: center;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .poster-image {
            max-width: 100%;
            max-height: 140px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s;
        }

        .poster-image:hover {
            transform: scale(1.05);
        }

        .poster-label {
            margin-top: 8px;
            font-size: 0.8em;
            color: #666;
            font-weight: 500;
        }

        .no-poster {
            height: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-style: italic;
            border: 2px dashed #ddd;
            border-radius: 8px;
            font-size: 0.9em;
        }

        /* 主要内容区域 */
        .card-main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* 卡片头部 */
        .card-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .card-checkbox {
            flex-shrink: 0;
            margin-top: 5px;
        }

        .card-checkbox input {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .card-basic-info {
            flex: 1;
        }

        .card-aux-info {
            display: flex;
            flex-direction: row;
            justify-content: space-around;
        }

        .card-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        /* 标题链接样式 */
        .card-title a {
            color: #333;
            text-decoration: none;
            transition: color 0.3s;
        }

        .card-title a:hover {
            color: #667eea;
            text-decoration: underline;
        }

        /* 标题编辑样式 */
        .card-title.editable {
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 4px;
            padding: 2px 4px;
            margin: -2px -4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-title.editable:hover {
            background: #f0f4ff;
            border: 1px solid #e1e5e9;
        }

        .card-title.editing {
            background: #f0f4ff;
            border: 2px solid #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .card-title.editing a,
        .card-title.editing .title-link-icon,
        .card-title.editing .title-actions {
            display: none !important;
        }

        .card-title.modified {
            color: #667eea;
            font-style: italic;
            position: relative;
        }



        .title-link-icon {
            font-size: 0.8em;
            color: #667eea;
            text-decoration: none;
            margin-left: 5px;
        }

        .title-edit-input {
            width: 100%;
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            line-height: 1.4;
            border: none;
            background: transparent;
            outline: none;
            font-family: inherit;
            padding: 0;
            margin: 0;
        }

        .title-edit-input:focus {
            background: white;
            border-radius: 4px;
            padding: 4px;
            margin: -4px;
        }


        .title-actions {
            display: inline-flex;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .title-editable:hover .title-actions {
            opacity: 1;
        }

        .title-action-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 0.8em;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .title-action-btn:hover {
            background: #e6e9ff;
        }


        /* 修改摘要样式 */
        .modification-summary {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 12px 16px;
            margin: 15px 0;
            color: #856404;
            font-size: 0.9em;
        }

        .modification-summary .summary-title {
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modification-summary .summary-list {
            max-height: 150px;
            overflow-y: auto;
            margin-top: 8px;
        }

        .modification-item {
            padding: 4px 0;
            border-bottom: 1px solid #f0e68c;
            font-size: 0.85em;
        }

        .modification-item:last-child {
            border-bottom: none;
        }

        .modification-item .original-title {
            color: #6c757d;
            text-decoration: line-through;
        }

        .modification-item .new-title {
            color: #667eea;
            font-weight: 500;
        }

        /* 重置按钮样式 */
        .btn-reset {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.85em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 8px;
        }

        .btn-reset:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(255, 193, 7, 0.4);
        }

        .btn-reset:active {
            transform: translateY(0);
        }

        .card-info {
            margin-bottom: 5px;
            color: #666;
            font-size: 0.95em;
        }

        .card-info strong {
            color: #333;
            margin-right: 5px;
        }

        /* 同步状态 */
        .sync-status {
            flex-shrink: 0;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
            margin-top: 5px;
        }

        .sync-status.synced {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .sync-status.not-synced {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }

        /* 详细信息区域 */
        .card-details {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        /* 场次和票档容器样式 */
        .sessions-container,
        .tickets-container {
            flex: 1;
            min-width: 250px;
            margin: 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        .section-title {
            font-size: 1.0em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 2px solid #667eea;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 场次列表样式 - 横向显示 */
        .sessions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .session-item {
            background: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 0.85em;
            color: #333;
            border-left: 3px solid #667eea;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
        }

        /* 剧目介绍样式 */
        .description-container {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        .description-text {
            color: #555;
            font-size: 0.9em;
            line-height: 1.5;
            max-height: 100px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .description-text.expanded {
            max-height: none;
        }

        .description-toggle {
            color: #667eea;
            cursor: pointer;
            font-size: 0.85em;
            margin-top: 8px;
            text-decoration: underline;
        }

        .description-toggle:hover {
            color: #5a67d8;
        }

        /* 票档列表样式 */
        .tickets-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .ticket-item {
            background: white;
            padding: 10px 12px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            min-width: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .ticket-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .ticket-info {
            text-align: center;
            width: 100%;
        }

        .ticket-price {
            font-weight: bold;
            font-size: 1.1em;
            color: #667eea;
            margin-bottom: 2px;
        }

        .ticket-description {
            font-size: 0.8em;
            color: #8c8c8c;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .ticket-status {
            font-size: 0.75em;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
            white-space: nowrap;
        }

        /* 票档状态样式 */
        .ticket-item.sold-out {
            border: 2px solid #ff6b6b;
            background: linear-gradient(135deg, #ffeded, #fff5f5);
            opacity: 0.8;
        }

        .ticket-item.sold-out .ticket-status {
            background: #ff6b6b;
            color: white;
        }

        .ticket-item.available {
            border: 2px solid #51cf66;
            background: linear-gradient(135deg, #f0fff4, #f8fff8);
        }

        .ticket-item.available .ticket-status {
            background: #51cf66;
            color: white;
        }

        .ticket-item.early-bird {
            border: 2px solid #ffa726;
            background: linear-gradient(135deg, #fff8e1, #fffbf0);
        }

        .ticket-item.early-bird .ticket-status {
            background: #ffa726;
            color: white;
        }

        .ticket-item.early-bird .ticket-original {
            font-size: 0.7em;
            color: #ff6b6b;
            text-decoration: line-through;
            margin-top: 4px;
        }

        /* 更多提示 */
        .more-sessions,
        .more-tickets {
            font-size: 0.85em;
            color: #667eea;
            cursor: pointer;
            text-align: center;
            padding: 6px 10px;
            background: white;
            border-radius: 6px;
            border: 1px dashed #667eea;
            transition: background 0.3s;
            white-space: nowrap;
        }

        .more-sessions:hover,
        .more-tickets:hover {
            background: #f0f4ff;
        }

        /* 无数据提示 */
        .no-data {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px dashed #ddd;
        }

        /* 统计信息样式 */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e1e5e9;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            font-weight: 500;
        }

        /* 加载状态样式 */
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        /* 演出日程矩阵样式 */
        .schedule-container {
            flex: 1;
            min-width: 250px;
            margin: 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        .date-group {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #ccc;
        }

        .date-header {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 2px solid #667eea;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .session-group {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #eee;
        }

        .session-header {
            font-size: 1.0em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            padding-bottom: 3px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .session-tickets {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .no-tickets {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 10px;
        }

        .ticket-item.sold-out .ticket-original {
            font-size: 0.7em;
            color: #ff6b6b;
            margin-top: 4px;
        }

        /* 联动选择器样式 */
        .interactive-schedule {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .schedule-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .schedule-title h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
        }

        .schedule-info {
            font-size: 12px;
            color: #7f8c8d;
            background: #ecf0f1;
            padding: 4px 8px;
            border-radius: 12px;
        }

        .schedule-selectors {
            display: grid;
            gap: 15px;
        }

        .selector-group {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .selector-label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .selector-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .selector-option {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 20px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            font-weight: 500;
            color: #495057;
            position: relative;
            min-height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .selector-option:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .selector-option.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .selector-option.active:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* 票档状态样式 */
        .selector-option.available {
            border-color: #28a745;
        }

        .selector-option.sold-out {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
            cursor: not-allowed;
        }

        .selector-option.early-bird {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }

        .discount-badge {
            background: #ff6b6b;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 5px;
            font-weight: bold;
        }

        .selector-option .ticket-status {
            font-size: 10px;
            margin-top: 2px;
            opacity: 0.8;
        }

        /* 选择摘要样式 */
        .selection-summary {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border: 2px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .current-selection {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .selection-text {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            flex: 1;
            min-width: 200px;
        }

        .selection-price {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .price-value {
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
        }

        .price-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .price-status.available {
            background: #d4edda;
            color: #155724;
        }

        .price-status.sold-out {
            background: #f8d7da;
            color: #721c24;
        }

        .price-status.early-bird {
            background: #fff3cd;
            color: #856404;
        }

        .no-schedule-data {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-style: italic;
            background: white;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }

        /* 标题编辑样式 */
        .title-editable {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .title-editable:hover {
            background-color: #f0f4ff;
            border-radius: 4px;
            padding: 2px 4px;
            margin: -2px -4px;
        }



        .title-editing {
            background: #f0f4ff;
            border: 2px solid #667eea;
            border-radius: 4px;
            padding: 2px 4px;
            margin: -2px -4px;
        }

        .title-input {
            width: 100%;
            border: none;
            background: transparent;
            font-size: inherit;
            font-weight: inherit;
            color: inherit;
            font-family: inherit;
            padding: 0;
            margin: 0;
            outline: none;
        }

        .title-modified {
            color: #667eea;
            font-style: italic;
        }



        .title-tooltip {
            position: absolute;
            top: 100%;
            left: 0;
            background: #2d3748;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8em;
            z-index: 100;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            white-space: normal;
            max-width: 300px;
            display: none;
            transition: all 0.3s ease;
        }

        .title-editable:hover .title-tooltip {
            display: block;
        }

        .title-modified .title-tooltip {
            background: #ff6b6b;
            font-weight: bold;
            border: 1px solid #e74c3c;
        }

        .title-actions {
            display: inline-block;
            margin-left: 10px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .title-editable:hover .title-actions {
            opacity: 1;
        }

        .title-action-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 0.8em;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .title-action-btn:hover {
            background: #e6e9ff;
        }

        .title-reset-btn {
            color: #ff6b6b;
        }

        /* 短剧名样式 */
        .card-short-name-container {
            margin: 8px 0;
        }

        .card-short-name {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .short-name-label {
            font-weight: 600;
            color: #555;
            flex-shrink: 0;
        }

        .short-name-text {
            flex: 1;
            color: #333;
            min-height: 1.2em;
        }

        .short-name-modified {
            color: #667eea;
            font-style: italic;
            font-weight: 500;
        }

        .short-name-editable {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .short-name-editable:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .short-name-editing {
            background: #f0f4ff;
            border: 2px solid #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .short-name-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: inherit;
            color: inherit;
            font-family: inherit;
            padding: 0;
            margin: 0;
            outline: none;
            min-height: 1.2em;
        }

        .short-name-actions {
            display: inline-flex;
            opacity: 0;
            transition: opacity 0.2s ease;
            gap: 4px;
        }

        .short-name-editable:hover .short-name-actions {
            opacity: 1;
        }

        .short-name-action-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 0.8em;
            padding: 2px 4px;
            border-radius: 3px;
            transition: background 0.2s;
        }

        .short-name-action-btn:hover {
            background: #e6e9ff;
        }

        .short-name-reset-btn {
            color: #ff6b6b;
        }

        .short-name-reset-btn:hover {
            background: #ffebee;
        }

        /* 同步对话框样式 */
        .sync-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .sync-dialog-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .sync-dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e1e5e9;
        }

        .sync-dialog-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .sync-dialog-close {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: #666;
        }

        .sync-dialog-content {
            margin-bottom: 20px;
        }

        .sync-dialog-summary {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .sync-dialog-changes {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .sync-change-item {
            padding: 10px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
        }

        .sync-change-item:last-child {
            border-bottom: none;
        }

        .sync-change-original {
            color: #666;
            text-decoration: line-through;
            margin-right: 10px;
        }

        .sync-change-arrow {
            margin: 0 10px;
            color: #667eea;
        }

        .sync-change-new {
            color: #667eea;
            font-weight: bold;
        }

        .sync-dialog-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .sync-dialog-btn {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            border: none;
        }

        .sync-dialog-btn-cancel {
            background: #e1e5e9;
            color: #333;
        }

        .sync-dialog-btn-reset {
            background: #ff6b6b;
            color: white;
        }

        .sync-dialog-btn-confirm {
            background: #667eea;
            color: white;
        }

        @media (max-width: 900px) {
            .main-content {
                grid-template-columns: 1fr;
                justify-content: stretch;
            }

            .config-panel,
            .status-panel {
                width: 100%;
                min-width: unset;
                max-width: unset;
            }

            .data-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .pagination {
                flex-wrap: wrap;
            }

            /* 响应式选择器样式 */
            .selector-options {
                justify-content: center;
            }

            .selector-option {
                min-width: 45%;
                text-align: center;
            }

            .current-selection {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }

            .selection-price {
                justify-content: center;
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>
    <!-- 移除了同步对话框 -->

    <div class="container">
        <div class="header">
            <h1> 爬虫管理界面</h1>
        </div>

        <div class="main-content">
            <!-- 配置面板 -->
            <div class="config-panel">
                <div class="panel-title">⚙️ 爬虫配置</div>

                <div class="form-group">
                    <label for="url">目标URL：</label>
                    <textarea id="url" rows="3" placeholder="留空使用默认大麦网话剧歌剧搜索页面"></textarea>
                </div>

                <div class="form-group">
                    <label for="city">城市：</label>
                    <select id="city">
                        <option value="">全部城市</option>
                        <option value="北京">北京</option>
                        <option value="上海">上海</option>
                        <option value="广州">广州</option>
                        <option value="深圳">深圳</option>
                        <option value="杭州">杭州</option>
                        <option value="成都">成都</option>
                        <option value="武汉">武汉</option>
                        <option value="西安">西安</option>
                        <option value="南京">南京</option>
                        <option value="天津">天津</option>
                        <option value="重庆">重庆</option>
                        <option value="苏州">苏州</option>
                        <option value="长沙">长沙</option>
                        <option value="青岛">青岛</option>
                        <option value="大连">大连</option>
                        <option value="厦门">厦门</option>
                        <option value="济南">济南</option>
                        <option value="哈尔滨">哈尔滨</option>
                        <option value="福州">福州</option>
                        <option value="昆明">昆明</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="category">分类：</label>
                    <select id="category">
                        <option value="">全部分类</option>
                        <option value="话剧歌剧">话剧歌剧</option>
                        <option value="音乐会">音乐会</option>
                        <option value="演唱会">演唱会</option>
                        <option value="舞蹈芭蕾">舞蹈芭蕾</option>
                        <option value="儿童亲子">儿童亲子</option>
                        <option value="曲苑杂坛">曲苑杂坛</option>
                        <option value="体育">体育</option>
                        <option value="展览休闲">展览休闲</option>
                        <option value="其他">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="time_period">时间范围：</label>
                    <select id="time_period">
                        <option value="">全部时间</option>
                        <option value="今天">今天</option>
                        <option value="明天">明天</option>
                        <option value="本周末">本周末</option>
                        <option value="本周">本周</option>
                        <option value="下周">下周</option>
                        <option value="本月">本月</option>
                        <option value="下月">下月</option>
                        <option value="近期">近期</option>
                        <option value="不限">不限</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="max_items">最大爬取数量：</label>
                    <input type="number" id="max_items" value="20" min="1" max="100">
                </div>

                <div class="form-group">
                    <label for="delay">请求间隔（秒）：</label>
                    <input type="number" id="delay" value="2.0" min="0.5" max="10" step="0.5">
                </div>

                <div class="form-group">
                    <label for="maxPages">最大页数：</label>
                    <input type="number" id="maxPages" value="10" min="1" max="50">
                </div>

                <div class="form-group">
                    <button id="startBtn" class="btn btn-primary">🚀 开始爬取</button>
                    <button id="stopBtn" class="btn btn-danger" disabled>⏹️ 停止爬取</button>
                </div>
            </div>

            <!-- 状态面板 -->
            <div class="status-panel">
                <div class="panel-title">📊 运行状态</div>

                <div class="status-info">
                    <div><strong>状态：</strong> <span id="status">准备就绪</span></div>
                    <div><strong>当前任务：</strong> <span id="currentItem">无</span></div>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%">
                            <div class="progress-text" id="progressText">0 / 0</div>
                        </div>
                    </div>
                </div>

                <div class="panel-title" style="margin-top: 25px;">📝 运行日志</div>
                <div class="log-container" id="logContainer">
                    <div class="log-entry">等待开始...</div>
                </div>
            </div>

            <!-- 数据展示区域 -->
            <div class="data-section">
                <div class="panel-title">📋 已爬取数据</div>

                <div class="stats" id="statsContainer">
                    <div class="stat-card">
                        <div class="stat-number" id="totalCount">0</div>
                        <div class="stat-label">总记录数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="showCount">0</div>
                        <div class="stat-label">演出项目</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="venueCount">0</div>
                        <div class="stat-label">场馆数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="syncedCount">0</div>
                        <div class="stat-label">已同步</div>
                    </div>
                </div>

                <!-- 数据控制区域 -->
                <div class="data-controls">
                    <div class="checkbox-group">
                        <input type="checkbox" id="selectAll">
                        <label for="selectAll">全选</label>
                    </div>
                    <div class="selection-info" id="selectionInfo">
                        已选择 0 条记录
                    </div>
                    <div class="filter-group">
                        <label for="syncFilter">同步状态：</label>
                        <select id="syncFilter">
                            <option value="all">全部</option>
                            <option value="synced">已同步</option>
                            <option value="not-synced">未同步</option>
                        </select>
                    </div>
                    <button id="syncBtn" class="btn btn-success" disabled>🔄 同步选中数据</button>
                    <button id="deleteBtn" class="btn btn-danger" disabled>🗑️ 删除选中记录</button>
                    <button id="refreshBtn" class="btn btn-secondary">🔄 刷新数据</button>
                </div>

                <!-- 分页控制 -->
                <div class="pagination">
                    <div class="pagination-info" id="paginationInfo">
                        显示第 1-20 条，共 0 条记录
                    </div>
                    <button class="pagination-btn" id="prevBtn" disabled>« 上一页</button>
                    <div id="pageNumbers"></div>
                    <button class="pagination-btn" id="nextBtn" disabled>下一页 »</button>
                </div>

                <div id="dataContainer" class="loading">
                    正在加载数据...
                </div>

                <!-- 底部分页控制 -->
                <div class="pagination">
                    <div class="pagination-info" id="paginationInfo2">
                        显示第 1-20 条，共 0 条记录
                    </div>
                    <button class="pagination-btn" id="prevBtn2" disabled>« 上一页</button>
                    <div id="pageNumbers2"></div>
                    <button class="pagination-btn" id="nextBtn2" disabled>下一页 »</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let statusInterval;
        let isRunning = false;
        let allData = [];
        let filteredData = [];
        let selectedIds = new Set();
        let currentPage = 1;
        let pageSize = 20;
        let totalPages = 0;

        // 标题修改跟踪系统
        let titleModifications = {};
        // 短剧名修改跟踪系统
        let shortNameModifications = {};

        // 短剧名提取规则
        function extractShortName(title) {
            if (!title || typeof title !== 'string') {
                return '';
            }

            // 清理标题，移除多余空格
            const cleanTitle = title.trim().replace(/\s+/g, ' ');

            // 规则1: 提取《》内的内容
            const bookTitleMatch = cleanTitle.match(/《([^》]+)》/);
            if (bookTitleMatch) {
                return bookTitleMatch[1].trim();
            }

            // 规则2: 提取【】内的内容
            const bracketMatch = cleanTitle.match(/【([^】]+)】/);
            if (bracketMatch) {
                return bracketMatch[1].trim();
            }

            // 规则3: 提取""内的内容
            const quoteMatch = cleanTitle.match(/"([^"]+)"/);
            if (quoteMatch) {
                return quoteMatch[1].trim();
            }

            // 规则4: 提取''内的内容
            const singleQuoteMatch = cleanTitle.match(/'([^']+)'/);
            if (singleQuoteMatch) {
                return singleQuoteMatch[1].trim();
            }

            // 规则5: 移除常见后缀词汇后的内容
            const suffixPatterns = [
                /(.+?)(?:\s*[-—]\s*.*)?$/,  // 移除破折号后的内容
                /(.+?)(?:\s*\|\s*.*)?$/,    // 移除竖线后的内容
                /(.+?)(?:\s*话剧.*)?$/,     // 移除"话剧"后的内容
                /(.+?)(?:\s*音乐剧.*)?$/,   // 移除"音乐剧"后的内容
                /(.+?)(?:\s*演唱会.*)?$/,   // 移除"演唱会"后的内容
                /(.+?)(?:\s*音乐会.*)?$/,   // 移除"音乐会"后的内容
                /(.+?)(?:\s*\d{4}.*)?$/,    // 移除年份后的内容
            ];

            for (const pattern of suffixPatterns) {
                const match = cleanTitle.match(pattern);
                if (match && match[1] && match[1].trim() !== cleanTitle) {
                    const extracted = match[1].trim();
                    if (extracted.length >= 2 && extracted.length <= 30) {
                        return extracted;
                    }
                }
            }

            // 规则6: 如果标题较短且没有特殊符号，直接使用
            if (cleanTitle.length <= 20 && !/[（）()【】《》""'']/.test(cleanTitle)) {
                return cleanTitle;
            }

            // 规则7: 提取第一个词或短语（最多15个字符）
            const firstPart = cleanTitle.substring(0, 15);
            const spaceIndex = firstPart.indexOf(' ');
            if (spaceIndex > 0 && spaceIndex < 15) {
                return firstPart.substring(0, spaceIndex);
            }

            // 默认返回前15个字符
            return cleanTitle.substring(0, 15);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function () {
            loadData();
            startStatusPolling();
            initializeEventListeners();
        });

        // 初始化事件监听器
        function initializeEventListeners() {
            // 全选复选框
            document.getElementById('selectAll').addEventListener('change', function () {
                const isChecked = this.checked;
                const checkboxes = document.querySelectorAll('.item-checkbox');

                checkboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                    const itemId = checkbox.dataset.itemId;
                    if (isChecked) {
                        selectedIds.add(itemId);
                    } else {
                        selectedIds.delete(itemId);
                    }
                });

                updateSelectionInfo();
                updateSyncButton();
                updateCardSelection();
            });

            // 同步状态过滤器
            document.getElementById('syncFilter').addEventListener('change', function () {
                currentPage = 1;
                filterAndDisplayData();
            });

            // 同步按钮
            document.getElementById('syncBtn').addEventListener('click', syncSelectedData);

            // 删除按钮
            document.getElementById('deleteBtn').addEventListener('click', deleteSelectedData);

            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', loadData);

            // 分页按钮
            document.getElementById('prevBtn').addEventListener('click', () => goToPage(currentPage - 1));
            document.getElementById('nextBtn').addEventListener('click', () => goToPage(currentPage + 1));
            document.getElementById('prevBtn2').addEventListener('click', () => goToPage(currentPage - 1));
            document.getElementById('nextBtn2').addEventListener('click', () => goToPage(currentPage + 1));
        }

        // 开始爬取
        document.getElementById('startBtn').addEventListener('click', function () {
            const url = document.getElementById('url').value.trim();
            const city = document.getElementById('city').value.trim();
            const category = document.getElementById('category').value.trim();
            const timePeriod = document.getElementById('time_period').value.trim();
            const maxItems = parseInt(document.getElementById('max_items').value);
            const delay = parseFloat(document.getElementById('delay').value);
            const maxPages = parseInt(document.getElementById('maxPages').value);

            if (maxItems < 1 || maxItems > 100) {
                alert('最大爬取数量必须在1-100之间');
                return;
            }

            if (delay < 0.5 || delay > 10) {
                alert('请求间隔必须在0.5-10秒之间');
                return;
            }

            if (maxPages < 1 || maxPages > 50) {
                alert('最大爬取页数必须在1-50之间');
                return;
            }

            console.log('发送爬取请求...', {
                url: url,
                city: city,
                category: category,
                time_period: timePeriod,
                max_items: maxItems,
                delay: delay,
                max_pages: maxPages
            });

            fetch('/crawler/start_crawl', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: url,
                    city: city,
                    category: category,
                    time_period: timePeriod,
                    max_items: maxItems,
                    delay: delay,
                    max_pages: maxPages
                })
            })
                .then(response => {
                    console.log('响应状态:', response.status, response.statusText);
                    console.log('响应头:', response.headers.get('content-type'));

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        return response.text().then(text => {
                            console.error('服务器返回非JSON数据:', text.substring(0, 200));
                            throw new Error('服务器返回格式错误（非JSON）');
                        });
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('解析的响应数据:', data);
                    if (data.success) {
                        isRunning = true;
                        updateButtons();
                        addLogEntry('✅ ' + data.message);
                    } else {
                        alert('启动失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('请求错误:', error);
                    addLogEntry('❌ 启动失败: ' + error.message);
                    alert('启动失败: ' + error.message);
                });
        });

        // 停止爬取
        document.getElementById('stopBtn').addEventListener('click', function () {
            fetch('/crawler/stop_crawl', {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    addLogEntry('⏹️ ' + data.message);
                    if (data.success) {
                        isRunning = false;
                        updateButtons();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });

        // 更新按钮状态
        function updateButtons() {
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');

            startBtn.disabled = isRunning;
            stopBtn.disabled = !isRunning;
        }

        // 更新选择信息
        function updateSelectionInfo() {
            const info = document.getElementById('selectionInfo');
            info.textContent = `已选择 ${selectedIds.size} 条记录`;
        }

        // 更新同步按钮状态
        function updateSyncButton() {
            const syncBtn = document.getElementById('syncBtn');
            const deleteBtn = document.getElementById('deleteBtn');
            const isDisabled = selectedIds.size === 0;

            syncBtn.disabled = isDisabled;
            deleteBtn.disabled = isDisabled;
        }

        // 更新卡片选择状态
        function updateCardSelection() {
            const cards = document.querySelectorAll('.data-card');
            cards.forEach(card => {
                const checkbox = card.querySelector('.item-checkbox');
                if (checkbox && checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        }

        // 开始状态轮询
        function startStatusPolling() {
            statusInterval = setInterval(updateStatus, 1000);
        }

        // 更新状态
        function updateStatus() {
            fetch('/crawler/status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // 更新运行状态
                    if (data.running !== isRunning) {
                        isRunning = data.running;
                        updateButtons();

                        // 如果爬取刚刚完成，刷新数据
                        if (!data.running && data.progress > 0) {
                            setTimeout(loadData, 1000);
                        }
                    }

                    // 更新状态显示
                    document.getElementById('status').textContent = data.message || '准备就绪';
                    document.getElementById('currentItem').textContent = data.current_item || '无';

                    // 更新进度条
                    const progress = data.total > 0 ? (data.progress / data.total) * 100 : 0;
                    document.getElementById('progressFill').style.width = progress + '%';
                    document.getElementById('progressText').textContent = `${data.progress || 0} / ${data.total || 0}`;

                    // 更新日志
                    if (data.logs && data.logs.length > 0) {
                        updateLogs(data.logs);
                    }

                    // 如果正在运行，实时更新数据（但不影响分页）
                    if (data.running && data.progress > 0) {
                        loadDataSilently();
                    }
                })
                .catch(error => {
                    console.error('Status update error:', error);
                    // 如果状态接口失败，降低轮询频率
                    if (statusInterval) {
                        clearInterval(statusInterval);
                        statusInterval = setInterval(updateStatus, 5000); // 改为5秒一次
                    }
                });
        }

        // 更新日志
        function updateLogs(logs) {
            const container = document.getElementById('logContainer');
            container.innerHTML = '';

            // 显示最新的50条日志
            logs.slice(-50).forEach(log => {
                const entry = document.createElement('div');
                entry.className = 'log-entry';
                entry.textContent = log;
                container.appendChild(entry);
            });

            // 滚动到最新日志
            container.scrollTop = container.scrollHeight;
        }

        // 添加日志条目
        function addLogEntry(message) {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // 加载数据
        function loadData() {
            // 显示加载状态
            document.getElementById('dataContainer').innerHTML = '<div class="loading">正在加载数据...</div>';

            // 并行加载数据和同步状态
            Promise.all([
                fetch('/crawler/data').then(response => {
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    return response.json();
                }),
                fetch('/crawler/sync/sync_status').then(response => {
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    return response.json();
                }).catch(() => ({ status: {} }))
            ])
                .then(([data, syncData]) => {
                    // 按crawl_time降序排序，最新的在前面
                    allData = data.items.sort((a, b) => {
                        const timeA = new Date(a.crawl_time || 0);
                        const timeB = new Date(b.crawl_time || 0);
                        return timeB - timeA;
                    });

                    window.syncStatusData = syncData.status || {};
                    updateStats(data);
                    currentPage = 1;
                    filterAndDisplayData();

                    // 清空之前的选择
                    selectedIds.clear();
                    document.getElementById('selectAll').checked = false;
                    updateSelectionInfo();
                    updateSyncButton();

                    // 清空标题修改记录，因为数据已经重新加载
                    titleModifications = {};
                    updateSyncButtonText();
                })
                .catch(error => {
                    console.error('Data loading error:', error);
                    document.getElementById('dataContainer').innerHTML =
                        '<div class="loading">数据加载失败，请稍后重试</div>';
                });
        }

        // 静默加载数据（不重置页面状态）
        function loadDataSilently() {
            Promise.all([
                fetch('/crawler/data').then(response => {
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    return response.json();
                }),
                fetch('/crawler/sync/sync_status').then(response => {
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    return response.json();
                }).catch(() => ({ status: {} }))
            ])
                .then(([data, syncData]) => {
                    const oldDataLength = allData.length;

                    // 按crawl_time降序排序，最新的在前面
                    allData = data.items.sort((a, b) => {
                        const timeA = new Date(a.crawl_time || 0);
                        const timeB = new Date(b.crawl_time || 0);
                        return timeB - timeA;
                    });

                    window.syncStatusData = syncData.status || {};
                    updateStats(data);

                    // 如果数据有更新，重新显示当前页
                    if (allData.length !== oldDataLength) {
                        filterAndDisplayData();
                    }
                })
                .catch(error => {
                    console.error('Silent data loading error:', error);
                    // 静默加载失败时不影响用户体验，只记录错误
                });
        }

        // 过滤并显示数据
        function filterAndDisplayData() {
            const syncFilter = document.getElementById('syncFilter').value;

            // 根据同步状态过滤数据
            if (syncFilter === 'all') {
                filteredData = allData;
            } else {
                filteredData = allData.filter(item => {
                    const syncStatus = getSyncStatus(item.id);
                    return syncFilter === 'synced' ? syncStatus.synced : !syncStatus.synced;
                });
            }

            // 计算分页
            totalPages = Math.ceil(filteredData.length / pageSize);
            if (currentPage > totalPages) {
                currentPage = totalPages || 1;
            }

            // 显示当前页数据
            displayCurrentPage();
            updatePaginationControls();
        }

        // 显示当前页数据
        function displayCurrentPage() {
            const container = document.getElementById('dataContainer');

            if (filteredData.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, filteredData.length);
            const currentPageData = filteredData.slice(startIndex, endIndex);

            container.innerHTML = '';
            currentPageData.forEach(item => {
                const card = createDataCard(item);
                container.appendChild(card);
            });
        }

        // 更新分页控制
        function updatePaginationControls() {
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredData.length);

            // 更新分页信息
            const paginationInfo = `显示第 ${startIndex}-${endIndex} 条，共 ${filteredData.length} 条记录`;
            document.getElementById('paginationInfo').textContent = paginationInfo;
            document.getElementById('paginationInfo2').textContent = paginationInfo;

            // 更新分页按钮
            document.getElementById('prevBtn').disabled = currentPage <= 1;
            document.getElementById('nextBtn').disabled = currentPage >= totalPages;
            document.getElementById('prevBtn2').disabled = currentPage <= 1;
            document.getElementById('nextBtn2').disabled = currentPage >= totalPages;

            // 更新页码按钮
            updatePageNumbers();
        }

        // 更新页码按钮
        function updatePageNumbers() {
            const pageNumbers = document.getElementById('pageNumbers');
            const pageNumbers2 = document.getElementById('pageNumbers2');

            pageNumbers.innerHTML = '';
            pageNumbers2.innerHTML = '';

            if (totalPages <= 1) return;

            // 计算显示的页码范围
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);

            // 如果开始页码大于1，显示第一页和省略号
            if (startPage > 1) {
                addPageButton(pageNumbers, 1);
                addPageButton(pageNumbers2, 1);
                if (startPage > 2) {
                    pageNumbers.appendChild(createEllipsis());
                    pageNumbers2.appendChild(createEllipsis());
                }
            }

            // 显示页码
            for (let i = startPage; i <= endPage; i++) {
                addPageButton(pageNumbers, i);
                addPageButton(pageNumbers2, i);
            }

            // 如果结束页码小于总页数，显示省略号和最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pageNumbers.appendChild(createEllipsis());
                    pageNumbers2.appendChild(createEllipsis());
                }
                addPageButton(pageNumbers, totalPages);
                addPageButton(pageNumbers2, totalPages);
            }
        }

        // 添加页码按钮
        function addPageButton(container, pageNum) {
            const button = document.createElement('button');
            button.className = `pagination-btn ${pageNum === currentPage ? 'active' : ''}`;
            button.textContent = pageNum;
            button.addEventListener('click', () => goToPage(pageNum));
            container.appendChild(button);
        }

        // 创建省略号
        function createEllipsis() {
            const span = document.createElement('span');
            span.textContent = '...';
            span.style.margin = '0 5px';
            return span;
        }

        // 跳转到指定页面
        function goToPage(pageNum) {
            if (pageNum < 1 || pageNum > totalPages) return;
            currentPage = pageNum;
            displayCurrentPage();
            updatePaginationControls();
        }

        // 更新统计信息
        function updateStats(data) {
            document.getElementById('totalCount').textContent = data.total;

            const venues = new Set();
            let showCount = 0;
            let syncedCount = 0;

            data.items.forEach(item => {
                if (item.venue && item.venue !== '待解析') {
                    venues.add(item.venue);
                }
                if (item.title && item.title !== '待解析') {
                    showCount++;
                }
                // 使用getSyncStatus函数获取同步状态
                const syncStatus = getSyncStatus(item.id);
                if (syncStatus.synced) {
                    syncedCount++;
                }
            });

            document.getElementById('showCount').textContent = showCount;
            document.getElementById('venueCount').textContent = venues.size;
            document.getElementById('syncedCount').textContent = syncedCount;
        }

        // 创建数据卡片
        function createDataCard(item) {
            const card = document.createElement('div');
            card.className = 'data-card';

            const title = item.title || '未知演出';
            const venue = item.venue || '未知场馆';
            const timePeriod = item.time_period || '未知时间';
            const poster = item.poster || '';
            const syncStatus = getSyncStatus(item.id);

            // 格式化场次信息
            const sessionsHtml = formatSessions(item.sessions);

            // 格式化票档信息
            const ticketTiersHtml = formatTicketTiers(item.ticket_tiers);

            // 创建联动选择器HTML
            const interactiveScheduleHtml = createInteractiveSchedule(item);

            // 获取或生成短剧名
            const originalShortName = item.shortName || extractShortName(title);
            const currentShortName = shortNameModifications[item.id] ?
                shortNameModifications[item.id].edited : originalShortName;

            // 创建可编辑标题
            const titleHtml = `<div class="card-title title-editable" data-item-id="${item.id}" data-original-title="${title}">
                ${item.url ?
                    `<span class="title-text">${title}</span><a href="${item.url}" target="_blank" title="点击查看原始页面" class="title-link-icon">🔗</a>` :
                    `<span class="title-text">${title}</span>`}
                <div class="title-actions">
                    <button class="title-action-btn title-edit-btn" title="编辑标题">✏️</button>
                </div>
            </div>`;

            // 创建可编辑短剧名
            const shortNameHtml = `<div class="card-short-name short-name-editable" data-item-id="${item.id}" data-original-short-name="${originalShortName}">
                <span class="short-name-label">短剧名：</span>
                <span class="short-name-text ${shortNameModifications[item.id] ? 'short-name-modified' : ''}">${currentShortName}</span>
                <div class="short-name-actions">
                    <button class="short-name-action-btn short-name-edit-btn" title="编辑短剧名">✏️</button>
                    ${shortNameModifications[item.id] ? '<button class="short-name-action-btn short-name-reset-btn" title="重置短剧名">↺</button>' : ''}
                </div>
            </div>`;

            card.innerHTML = `
                <div class="card-poster">
                    ${createPosterDisplay(poster)}
                </div>
                <div class="card-main-content">
                    <div class="card-header">
                        <div class="card-checkbox">
                            <input type="checkbox" class="item-checkbox" data-item-id="${item.id}">
                        </div>
                        
                        <div class="card-basic-info">
                            <div class="card-title">${titleHtml}</div>
                            <div class="card-short-name-container">${shortNameHtml}</div>
                            <div class="card-aux-info">
                                <div class="card-info"><strong>场馆：</strong>${venue}</div>
                                <div class="card-info"><strong>时间：</strong>${timePeriod}</div>
                                <div class="card-info"><strong>ID：</strong>${item.id}</div>
                            </div>
                        </div>
                        <div class="sync-status ${syncStatus.synced ? 'synced' : 'not-synced'}">
                            ${syncStatus.synced ? '✅ 已同步' : '❌ 未同步'}
                        </div>
                    </div>
                    
                    <div class="card-details">
                        ${interactiveScheduleHtml}
                    </div>
                    
                    ${createDescriptionDisplay(item.show_description)}
                </div>
            `;

            // 添加复选框事件监听器
            const checkbox = card.querySelector('.item-checkbox');
            checkbox.addEventListener('change', function () {
                const itemId = this.dataset.itemId;
                if (this.checked) {
                    selectedIds.add(itemId);
                    card.classList.add('selected');
                } else {
                    selectedIds.delete(itemId);
                    card.classList.remove('selected');
                }
                updateSelectionInfo();
                updateSyncButton();

                // 更新全选复选框状态
                const totalCheckboxes = document.querySelectorAll('.item-checkbox').length;
                const selectAllCheckbox = document.getElementById('selectAll');
                selectAllCheckbox.checked = selectedIds.size === totalCheckboxes;
            });

            // 添加标题编辑功能
            const titleElement = card.querySelector('.card-title');
            const titleTextElement = titleElement.querySelector('.title-text');
            const titleEditBtn = titleElement.querySelector('.title-edit-btn');

            // 点击标题或编辑按钮启用编辑
            function enableTitleEditing() {
                // 如果已经在编辑中，不做任何操作
                if (titleElement.classList.contains('title-editing')) return;

                const originalTitle = titleElement.getAttribute('data-original-title');
                const currentTitle = titleModifications[item.id] ?
                    titleModifications[item.id].edited : originalTitle;

                // 保存当前文本
                const currentText = titleTextElement.textContent;

                // 创建输入框
                titleElement.classList.add('title-editing');
                const inputElement = document.createElement('input');
                inputElement.type = 'text';
                inputElement.className = 'title-input';
                inputElement.value = currentText;
                inputElement.maxLength = 100;

                // 替换文本元素为输入框
                titleTextElement.innerHTML = '';
                titleTextElement.appendChild(inputElement);

                // 聚焦输入框
                inputElement.focus();
                inputElement.select();

                // 添加键盘事件处理
                inputElement.addEventListener('keydown', function (e) {
                    if (e.key === 'Enter') {
                        // 保存更改
                        saveTitleEdit();
                        e.preventDefault();
                    } else if (e.key === 'Escape') {
                        // 取消编辑
                        cancelTitleEdit();
                        e.preventDefault();
                    }
                });

                // 失去焦点时保存
                inputElement.addEventListener('blur', function () {
                    saveTitleEdit();
                });

                /**
                 * 验证标题
                 * @param {string} title - 要验证的标题
                 * @returns {Object} 验证结果，包含 isValid 和 message
                 */
                function validateTitle(title) {
                    if (!title || title.trim() === '') {
                        return { isValid: false, message: '标题不能为空' };
                    }

                    if (title.length > 100) {
                        return { isValid: false, message: '标题长度不能超过100个字符' };
                    }

                    // 检查特殊字符
                    const invalidChars = /[<>]/g;
                    if (invalidChars.test(title)) {
                        return { isValid: false, message: '标题不能包含 < 或 > 字符' };
                    }

                    return { isValid: true };
                }

                // 保存编辑
                function saveTitleEdit() {
                    let newTitle = inputElement.value.trim();

                    // 如果标题为空，使用原始标题
                    if (!newTitle) {
                        newTitle = originalTitle;
                    }

                    try {
                        // 更新标题文本
                        titleTextElement.textContent = newTitle;
                        titleElement.classList.remove('title-editing');

                        // 如果标题有变化，记录修改
                        if (newTitle !== originalTitle) {
                            trackTitleModification(item.id, originalTitle, newTitle);
                        } else {
                            deleteTitleModification(item.id);
                        }
                    } catch (error) {
                        console.error('保存标题时出错:', error);
                        cancelTitleEdit();
                    }
                }

                // 取消编辑
                function cancelTitleEdit() {
                    // 恢复原始文本
                    const displayTitle = titleModifications[item.id] ?
                        titleModifications[item.id].edited : originalTitle;
                    titleTextElement.textContent = displayTitle;
                    titleElement.classList.remove('title-editing');
                }
            }

            // 点击标题区域启用编辑
            titleElement.addEventListener('click', function (e) {
                // 如果点击的是链接、按钮或已经在编辑中，不启用编辑
                if (e.target.closest('a') || e.target.closest('button') ||
                    titleElement.classList.contains('title-editing')) {
                    return;
                }
                enableTitleEditing();
            });

            // 编辑按钮点击事件
            titleEditBtn.addEventListener('click', function (e) {
                e.stopPropagation(); // 阻止冒泡到标题元素
                enableTitleEditing();
            });

            // 添加短剧名编辑功能
            const shortNameElement = card.querySelector('.card-short-name');
            const shortNameTextElement = shortNameElement.querySelector('.short-name-text');
            const shortNameEditBtn = shortNameElement.querySelector('.short-name-edit-btn');
            const shortNameResetBtn = shortNameElement.querySelector('.short-name-reset-btn');

            // 点击短剧名或编辑按钮启用编辑
            function enableShortNameEditing() {
                // 如果已经在编辑中，不做任何操作
                if (shortNameElement.classList.contains('short-name-editing')) return;

                const originalShortName = shortNameElement.getAttribute('data-original-short-name');
                const currentShortName = shortNameModifications[item.id] ?
                    shortNameModifications[item.id].edited : originalShortName;

                // 保存当前文本
                const currentText = shortNameTextElement.textContent;

                // 创建输入框
                shortNameElement.classList.add('short-name-editing');
                const inputElement = document.createElement('input');
                inputElement.type = 'text';
                inputElement.className = 'short-name-input';
                inputElement.value = currentText;
                inputElement.maxLength = 50;

                // 替换文本元素为输入框
                shortNameTextElement.innerHTML = '';
                shortNameTextElement.appendChild(inputElement);

                // 聚焦输入框
                inputElement.focus();
                inputElement.select();

                // 添加键盘事件处理
                inputElement.addEventListener('keydown', function (e) {
                    if (e.key === 'Enter') {
                        // 保存更改
                        saveShortNameEdit();
                        e.preventDefault();
                    } else if (e.key === 'Escape') {
                        // 取消编辑
                        cancelShortNameEdit();
                        e.preventDefault();
                    }
                });

                // 失去焦点时保存
                inputElement.addEventListener('blur', function () {
                    saveShortNameEdit();
                });

                // 保存编辑
                function saveShortNameEdit() {
                    let newShortName = inputElement.value.trim();

                    // 如果短剧名为空，使用原始短剧名
                    if (!newShortName) {
                        newShortName = originalShortName;
                    }

                    try {
                        // 更新短剧名文本
                        shortNameTextElement.textContent = newShortName;
                        shortNameElement.classList.remove('short-name-editing');

                        // 如果短剧名有变化，记录修改
                        if (newShortName !== originalShortName) {
                            trackShortNameModification(item.id, originalShortName, newShortName);
                            shortNameTextElement.classList.add('short-name-modified');
                            // 添加重置按钮
                            if (!shortNameResetBtn) {
                                const resetBtn = document.createElement('button');
                                resetBtn.className = 'short-name-action-btn short-name-reset-btn';
                                resetBtn.title = '重置短剧名';
                                resetBtn.innerHTML = '↺';
                                resetBtn.addEventListener('click', function (e) {
                                    e.stopPropagation();
                                    resetShortName();
                                });
                                shortNameElement.querySelector('.short-name-actions').appendChild(resetBtn);
                            }
                        } else {
                            deleteShortNameModification(item.id);
                            shortNameTextElement.classList.remove('short-name-modified');
                            // 移除重置按钮
                            if (shortNameResetBtn) {
                                shortNameResetBtn.remove();
                            }
                        }
                    } catch (error) {
                        console.error('保存短剧名时出错:', error);
                        cancelShortNameEdit();
                    }
                }

                // 取消编辑
                function cancelShortNameEdit() {
                    // 恢复原始文本
                    const displayShortName = shortNameModifications[item.id] ?
                        shortNameModifications[item.id].edited : originalShortName;
                    shortNameTextElement.textContent = displayShortName;
                    shortNameElement.classList.remove('short-name-editing');
                }
            }

            // 重置短剧名
            function resetShortName() {
                const originalShortName = shortNameElement.getAttribute('data-original-short-name');
                shortNameTextElement.textContent = originalShortName;
                shortNameTextElement.classList.remove('short-name-modified');
                deleteShortNameModification(item.id);
                // 移除重置按钮
                if (shortNameResetBtn) {
                    shortNameResetBtn.remove();
                }
            }

            // 点击短剧名区域启用编辑
            shortNameElement.addEventListener('click', function (e) {
                // 如果点击的是按钮或已经在编辑中，不启用编辑
                if (e.target.closest('button') || shortNameElement.classList.contains('short-name-editing')) {
                    return;
                }
                enableShortNameEditing();
            });

            // 编辑按钮点击事件
            shortNameEditBtn.addEventListener('click', function (e) {
                e.stopPropagation(); // 阻止冒泡到短剧名元素
                enableShortNameEditing();
            });

            // 重置按钮点击事件
            if (shortNameResetBtn) {
                shortNameResetBtn.addEventListener('click', function (e) {
                    e.stopPropagation();
                    resetShortName();
                });
            }

            return card;
        }

        // 创建海报显示组件
        function createPosterDisplay(posterUrl) {
            if (!posterUrl) {
                return '<div class="poster-container"><div class="no-poster">暂无海报</div></div>';
            }

            return `
                <div class="poster-container">
                    <img src="${posterUrl}" alt="演出海报" class="poster-image">
                    <div class="poster-label">演出海报</div>
                </div>
            `;
        }

        // 创建剧目介绍显示组件
        function createDescriptionDisplay(description) {
            if (!description || description === '待解析') {
                return '';
            }

            // 生成唯一ID
            const descId = 'desc_' + Math.random().toString(36).substr(2, 9);
            const isLong = description.length > 100;

            return `
                <div class="description-container">
                    <div class="section-title">📖 剧目介绍</div>
                    <div class="description-text ${isLong ? '' : 'expanded'}" id="${descId}">
                        ${description}
                    </div>
                    ${isLong ? `<div class="description-toggle" onclick="toggleDescription('${descId}')">展开更多</div>` : ''}
                </div>
            `;
        }

        // 切换剧目介绍展开/收起
        function toggleDescription(descId) {
            const descElement = document.getElementById(descId);
            const toggleElement = descElement.nextElementSibling;

            if (descElement.classList.contains('expanded')) {
                descElement.classList.remove('expanded');
                toggleElement.textContent = '展开更多';
            } else {
                descElement.classList.add('expanded');
                toggleElement.textContent = '收起';
            }
        }

        // 格式化场次信息
        function formatSessions(sessions) {
            if (!sessions || sessions.length === 0) {
                return '<div class="sessions-container"><div class="section-title">🎭 场次信息</div><div class="no-data">暂无场次信息</div></div>';
            }

            let sessionsHtml = '<div class="sessions-container"><div class="section-title">🎭 场次信息</div><div class="sessions-list">';

            sessions.slice(0, 6).forEach((session, index) => {
                // 统一处理场次数据，只显示标准的日期时间格式
                let sessionText = '';
                if (typeof session === 'object' && session !== null) {
                    // 对象格式：{datetime: '2025-07-20 星期日 14:30', available: true}
                    if (session.datetime) {
                        sessionText = session.datetime;
                    } else if (session.time) {
                        sessionText = session.time;
                    } else {
                        sessionText = JSON.stringify(session);
                    }
                } else if (typeof session === 'string') {
                    // 字符串格式：直接使用
                    sessionText = session;
                }

                // 确保只显示标准格式：2025-07-20 星期日 14:30
                const cleanMatch = sessionText.match(/(\d{4}-\d{2}-\d{2}\s+星期[一二三四五六日]\s+\d{2}:\d{2})/);
                if (cleanMatch) {
                    sessionText = cleanMatch[1];
                }

                if (sessionText) {
                    sessionsHtml += `<div class="session-item">${sessionText}</div>`;
                }
            });

            if (sessions.length > 6) {
                sessionsHtml += `<div class="more-sessions">还有 ${sessions.length - 6} 个场次</div>`;
            }

            sessionsHtml += '</div></div>';
            return sessionsHtml;
        }

        // 格式化票档信息
        function formatTicketTiers(ticketTiers) {
            if (!ticketTiers || ticketTiers.length === 0) {
                return '<div class="tickets-container"><div class="section-title">🎫 票档信息</div><div class="no-data">暂无票档信息</div></div>';
            }

            // 按价格排序
            const sortedTiers = [...ticketTiers].sort((a, b) => (a.price || 0) - (b.price || 0));

            let ticketsHtml = '<div class="tickets-container"><div class="section-title">🎫 票档信息</div><div class="tickets-list">';

            sortedTiers.forEach(tier => {
                // 确定状态样式
                let statusClass = 'available';
                if (tier.status && (tier.status.includes('缺货') || tier.status.includes('售罄'))) {
                    statusClass = 'sold-out';
                } else if (tier.status && tier.status.includes('早鸟')) {
                    statusClass = 'early-bird';
                }

                // 构建显示文本
                let displayText = tier.description || `${tier.price}元`;
                let statusText = tier.status || '可购买';

                ticketsHtml += `
                    <div class="ticket-item ${statusClass}">
                        <div class="ticket-info">
                            <div class="ticket-price">¥${tier.price}</div>
                            <div class="ticket-description">${displayText}</div>
                        </div>
                        <div class="ticket-status">${statusText}</div>
                    </div>
                `;
            });

            ticketsHtml += '</div></div>';
            return ticketsHtml;
        }

        // 创建交互式城市-日期-场次-票档联动选择器
        function createInteractiveSchedule(item) {
            const scheduleData = analyzeScheduleStructure(item);

            if (!scheduleData.hasData) {
                return '<div class="interactive-schedule"><div class="no-schedule-data">暂无演出安排信息</div></div>';
            }

            const itemId = item.id;

            return `
                <div class="interactive-schedule" data-item-id="${itemId}">
                    <div class="schedule-title">
                        <h4>📅 演出安排选择</h4>
                        <span class="schedule-info">${scheduleData.summary}</span>
                    </div>
                    
                    <div class="schedule-selectors">
                        ${createSessionSelector(itemId, scheduleData.sessions)}
                    </div>
                </div>
            `;
        }

        // 分析数据结构，确定显示方式
        function analyzeScheduleStructure(item) {
            const result = {
                hasData: false,
                hasCities: false,
                hasDates: false,
                cities: [],
                dates: [],
                sessions: [],
                tickets: [],
                summary: ''
            };

            // 分析城市
            if (item.cities && item.cities.length > 0) {
                result.hasCities = true;
                result.cities = item.cities;
            }

            // 分析日期
            if (item.dates && item.dates.length > 1) {
                result.hasDates = true;
                result.dates = item.dates.sort();
            }

            // 分析场次
            if (item.sessions && item.sessions.length > 0) {
                result.sessions = item.sessions;
                result.hasData = true;
            }

            // 分析票档
            if (item.ticket_tiers && item.ticket_tiers.length > 0) {
                result.tickets = item.ticket_tiers.sort((a, b) => (a.price || 0) - (b.price || 0));
            }

            // 生成摘要
            const parts = [];
            if (result.hasCities) parts.push(`${result.cities.length}个城市`);
            if (result.hasDates) parts.push(`${result.dates.length}个日期`);
            if (result.sessions.length > 0) parts.push(`${result.sessions.length}个场次`);
            if (result.tickets.length > 0) parts.push(`${result.tickets.length}个票档`);

            result.summary = parts.join(' • ');

            return result;
        }

        // 创建城市选择器
        function createCitySelector(itemId, cities) {
            const cityOptions = cities.map((city, index) => {
                const cityName = city.name || city;
                const isFirst = index === 0;
                return `<button class="selector-option ${isFirst ? 'active' : ''}" 
                               data-type="city" 
                               data-value="${cityName}" 
                               data-item-id="${itemId}">
                    🏙️ ${cityName}
                </button>`;
            }).join('');

            return `
                <div class="selector-group">
                    <div class="selector-label">选择城市</div>
                    <div class="selector-options" data-type="city">
                        ${cityOptions}
                    </div>
                </div>
            `;
        }

        // 创建日期选择器
        function createDateSelector(itemId, dates) {
            const dateOptions = dates.map((date, index) => {
                const isFirst = index === 0;
                const displayDate = formatDateDisplay(date);
                return `<button class="selector-option ${isFirst ? 'active' : ''}" 
                               data-type="date" 
                               data-value="${date}" 
                               data-item-id="${itemId}">
                    📅 ${displayDate}
                </button>`;
            }).join('');

            return `
                <div class="selector-group">
                    <div class="selector-label">选择日期</div>
                    <div class="selector-options" data-type="date">
                        ${dateOptions}
                    </div>
                </div>
            `;
        }

        // 创建场次选择器
        function createSessionSelector(itemId, sessions) {
            // 初始显示所有场次，后续通过联动筛选
            const sessionOptions = sessions.map((session, index) => {
                const isFirst = index === 0;
                const displayText = session.name || session.session_time || '未知场次';
                const discountBadge = session.has_discount ? '<span class="discount-badge">惠</span>' : '';

                return `<button class="selector-option ${isFirst ? 'active' : ''}" 
                               data-type="session" 
                               data-value="${session.id}" 
                               data-session='${JSON.stringify(session)}' 
                               data-item-id="${itemId}">
                    🎭 ${displayText} ${discountBadge}
                </button>`;
            }).join('');

            return `
                <div class="selector-group">
                    <div class="selector-label">场次信息</div>
                    <div class="selector-options" data-type="session">
                        ${sessionOptions}
                    </div>
                </div>
            `;
        }

        // 创建票档选择器
        function createTicketSelector(itemId, tickets) {
            const ticketOptions = tickets.map((ticket, index) => {
                const isFirst = index === 0;
                const price = ticket.price || 0;
                const status = ticket.status || '可购买';
                const statusClass = getTicketStatusClass(status);
                const typeText = ticket.type === '双人套票' ? '套票' : '';

                return `<button class="selector-option ${isFirst ? 'active' : ''} ${statusClass}" 
                               data-type="ticket" 
                               data-value="${price}" 
                               data-ticket='${JSON.stringify(ticket)}' 
                               data-item-id="${itemId}">
                    🎫 ${price}元 ${typeText}
                    <span class="ticket-status">${status}</span>
                </button>`;
            }).join('');

            return `
                <div class="selector-group">
                    <div class="selector-label">选择票档</div>
                    <div class="selector-options" data-type="ticket">
                        ${ticketOptions}
                    </div>
                </div>
            `;
        }

        // 格式化日期显示
        function formatDateDisplay(dateStr) {
            try {
                const date = new Date(dateStr);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
                const weekday = weekdays[date.getDay()];
                return `${month}/${day} 周${weekday}`;
            } catch (e) {
                return dateStr;
            }
        }

        // 格式化演出日程信息（保留原功能作为备用）
        function formatScheduleMatrix(scheduleMatrix) {
            if (!scheduleMatrix || Object.keys(scheduleMatrix).length === 0) {
                return '<div class="schedule-container"><div class="section-title">📅 演出日程</div><div class="no-data">暂无日程信息</div></div>';
            }

            let scheduleHtml = '<div class="schedule-container"><div class="section-title">📅 演出日程</div>';

            // 检测数据结构类型
            const firstKey = Object.keys(scheduleMatrix)[0];
            const firstValue = scheduleMatrix[firstKey];

            // 如果第一个值有 session 属性，说明是扁平结构（直接按 sessionId）
            if (firstValue && firstValue.session) {
                // 扁平结构：直接遍历 sessionId
                scheduleHtml += `<div class="date-group">`;
                scheduleHtml += `<div class="date-header">📅 演出场次</div>`;

                for (const sessionId in scheduleMatrix) {
                    const sessionData = scheduleMatrix[sessionId];
                    if (!sessionData || !sessionData.session) continue;

                    const session = sessionData.session;
                    const tickets = sessionData.tickets || [];

                    // 安全检查 session.name
                    const sessionName = session.name || session.session_time || session.date_display || sessionId || '未知场次';

                    scheduleHtml += `<div class="session-group">`;
                    scheduleHtml += `<div class="session-header">🎭 ${sessionName}</div>`;

                    // 显示票档
                    if (tickets.length > 0) {
                        scheduleHtml += `<div class="session-tickets">`;
                        for (const ticket of tickets) {
                            const statusClass = getTicketStatusClass(ticket.status);
                            scheduleHtml += `<div class="ticket-item ${statusClass}">`;
                            scheduleHtml += `<div class="ticket-price">${ticket.price}元</div>`;
                            scheduleHtml += `<div class="ticket-status">${ticket.status || '可购买'}</div>`;
                            if (ticket.original_price && ticket.original_price !== ticket.price) {
                                scheduleHtml += `<div class="ticket-original">原价${ticket.original_price}元</div>`;
                            }
                            scheduleHtml += `</div>`;
                        }
                        scheduleHtml += `</div>`;
                    } else {
                        scheduleHtml += `<div class="no-tickets">暂无票档信息</div>`;
                    }

                    scheduleHtml += `</div>`; // session-group
                }
                scheduleHtml += `</div>`; // date-group
            } else {
                // 嵌套结构：按日期分组
                const sortedDates = Object.keys(scheduleMatrix).sort();

                for (const date of sortedDates) {
                    const sessions = scheduleMatrix[date];
                    if (!sessions || typeof sessions !== 'object') continue;

                    scheduleHtml += `<div class="date-group">`;
                    scheduleHtml += `<div class="date-header">📅 ${date}</div>`;

                    // 处理每个日期下的场次
                    for (const sessionId in sessions) {
                        const sessionData = sessions[sessionId];
                        if (!sessionData || !sessionData.session) continue;

                        const session = sessionData.session;
                        const tickets = sessionData.tickets || [];

                        // 安全检查 session.name
                        const sessionName = session.name || session.session_time || session.date_display || sessionId || '未知场次';

                        scheduleHtml += `<div class="session-group">`;
                        scheduleHtml += `<div class="session-header">🎭 ${sessionName}</div>`;

                        // 显示票档
                        if (tickets.length > 0) {
                            scheduleHtml += `<div class="session-tickets">`;
                            for (const ticket of tickets) {
                                const statusClass = getTicketStatusClass(ticket.status);
                                scheduleHtml += `<div class="ticket-item ${statusClass}">`;
                                scheduleHtml += `<div class="ticket-price">${ticket.price}元</div>`;
                                scheduleHtml += `<div class="ticket-status">${ticket.status || '可购买'}</div>`;
                                if (ticket.original_price && ticket.original_price !== ticket.price) {
                                    scheduleHtml += `<div class="ticket-original">原价${ticket.original_price}元</div>`;
                                }
                                scheduleHtml += `</div>`;
                            }
                            scheduleHtml += `</div>`;
                        } else {
                            scheduleHtml += `<div class="no-tickets">暂无票档信息</div>`;
                        }

                        scheduleHtml += `</div>`; // session-group
                    }

                    scheduleHtml += `</div>`; // date-group
                }
            }

            scheduleHtml += '</div>'; // schedule-container
            return scheduleHtml;
        }

        // 获取票档状态样式
        function getTicketStatusClass(status) {
            if (!status) return 'available';

            const statusLower = status.toLowerCase();
            if (statusLower.includes('缺货') || statusLower.includes('售罄')) {
                return 'sold-out';
            } else if (statusLower.includes('早鸟')) {
                return 'early-bird';
            } else {
                return 'available';
            }
        }

        // 获取同步状态
        function getSyncStatus(itemId) {
            // 从全局同步状态数据中获取状态
            if (window.syncStatusData && window.syncStatusData[itemId]) {
                return { synced: window.syncStatusData[itemId] };
            }
            return { synced: false };
        }

        // 初始化联动选择器事件监听
        function initInteractiveScheduleEvents() {
            document.addEventListener('click', function (e) {
                if (e.target.classList.contains('selector-option')) {
                    handleSelectorClick(e.target);
                }
            });
        }

        // 处理选择器点击事件
        function handleSelectorClick(button) {
            const itemId = button.getAttribute('data-item-id');
            const type = button.getAttribute('data-type');
            const value = button.getAttribute('data-value');

            // 同组内取消其他选中，设置当前选中
            const group = button.parentElement;
            group.querySelectorAll('.selector-option').forEach(opt => opt.classList.remove('active'));
            button.classList.add('active');

            // 根据选择类型进行联动
            updateLinkedSelectors(itemId, type, value);

            // 更新选择摘要
            updateSelectionSummary(itemId);
        }

        // 根据选择更新关联选择器
        function updateLinkedSelectors(itemId, changedType, changedValue) {
            const scheduleContainer = document.querySelector(`[data-item-id="${itemId}"]`);
            if (!scheduleContainer) return;

            const currentSelection = getCurrentSelection(itemId);

            // 根据选择的类型进行不同的联动逻辑
            switch (changedType) {
                case 'city':
                    updateDatesByCity(itemId, changedValue);
                    updateSessionsBySelection(itemId, currentSelection);
                    break;
                case 'date':
                    updateSessionsBySelection(itemId, currentSelection);
                    break;
                case 'session':
                    // 场次选择不需要更新其他选择器
                    break;
                case 'ticket':
                    // 票档选择不需要更新其他选择器
                    break;
            }
        }

        // 获取当前选择状态
        function getCurrentSelection(itemId) {
            const scheduleContainer = document.querySelector(`[data-item-id="${itemId}"]`);
            if (!scheduleContainer) return {};

            const selection = {};

            // 获取城市选择
            const cityButton = scheduleContainer.querySelector('[data-type="city"].active');
            if (cityButton) {
                selection.city = cityButton.getAttribute('data-value');
            }

            // 获取日期选择
            const dateButton = scheduleContainer.querySelector('[data-type="date"].active');
            if (dateButton) {
                selection.date = dateButton.getAttribute('data-value');
            }

            // 获取场次选择
            const sessionButton = scheduleContainer.querySelector('[data-type="session"].active');
            if (sessionButton) {
                selection.sessionId = sessionButton.getAttribute('data-value');
                try {
                    selection.session = JSON.parse(sessionButton.getAttribute('data-session'));
                } catch (e) {
                    selection.session = {};
                }
            }

            // 获取票档选择
            const ticketButton = scheduleContainer.querySelector('[data-type="ticket"].active');
            if (ticketButton) {
                selection.ticketPrice = ticketButton.getAttribute('data-value');
                try {
                    selection.ticket = JSON.parse(ticketButton.getAttribute('data-ticket'));
                } catch (e) {
                    selection.ticket = {};
                }
            }

            return selection;
        }

        // 根据城市更新日期选项
        function updateDatesByCity(itemId, cityName) {
            // 这里可以根据实际数据结构实现城市-日期关联逻辑
            // 暂时保持所有日期可选
            console.log(`城市 ${cityName} 选择变更，日期选项保持不变`);
        }

        // 根据当前选择更新场次选项
        function updateSessionsBySelection(itemId, selection) {
            const scheduleContainer = document.querySelector(`[data-item-id="${itemId}"]`);
            const sessionOptions = scheduleContainer.querySelectorAll('[data-type="session"]');

            sessionOptions.forEach(button => {
                let shouldShow = true;

                try {
                    const session = JSON.parse(button.getAttribute('data-session'));

                    // 根据城市筛选
                    if (selection.city && session.city && session.city !== selection.city) {
                        shouldShow = false;
                    }

                    // 根据日期筛选
                    if (selection.date && session.date && session.date !== selection.date) {
                        shouldShow = false;
                    }

                    // 显示或隐藏选项
                    button.style.display = shouldShow ? 'block' : 'none';

                    // 如果当前选中的场次被隐藏，取消选中
                    if (!shouldShow && button.classList.contains('active')) {
                        button.classList.remove('active');
                        // 自动选择第一个可见的场次
                        const firstVisible = button.parentElement.querySelector('[data-type="session"]:not([style*="none"])');
                        if (firstVisible) {
                            firstVisible.classList.add('active');
                        }
                    }
                } catch (e) {
                    console.warn('解析场次数据失败:', e);
                }
            });
        }

        // 更新选择摘要
        function updateSelectionSummary(itemId) {
            const summaryContainer = document.getElementById(`summary-${itemId}`);
            if (!summaryContainer) return;

            const selection = getCurrentSelection(itemId);
            const selectionText = summaryContainer.querySelector('.selection-text');
            const selectionPrice = summaryContainer.querySelector('.selection-price');

            // 构建选择描述
            const parts = [];
            if (selection.city) parts.push(`📍 ${selection.city}`);
            if (selection.date) parts.push(`📅 ${formatDateDisplay(selection.date)}`);
            if (selection.session && selection.session.name) {
                const sessionName = selection.session.name;
                const discountText = selection.session.has_discount ? ' (优惠)' : '';
                parts.push(`🎭 ${sessionName}${discountText}`);
            }

            if (parts.length > 0) {
                selectionText.textContent = parts.join(' ');
            } else {
                selectionText.textContent = '请选择演出安排';
            }

            // 显示价格信息
            if (selection.ticket && selection.ticket.price) {
                const price = selection.ticket.price;
                const status = selection.ticket.status || '可购买';
                const typeText = selection.ticket.type === '双人套票' ? ' (套票)' : '';

                selectionPrice.innerHTML = `
                        <span class="price-value">¥${price}${typeText}</span>
                        <span class="price-status ${getTicketStatusClass(status)}">${status}</span>
                    `;
                selectionPrice.style.display = 'block';
            } else {
                selectionPrice.style.display = 'none';
            }
        }

        // 页面加载完成后初始化事件
        document.addEventListener('DOMContentLoaded', function () {
            initInteractiveScheduleEvents();

            // 为已存在的选择器初始化默认选择
            setTimeout(() => {
                document.querySelectorAll('.interactive-schedule').forEach(schedule => {
                    const itemId = schedule.getAttribute('data-item-id');
                    updateSelectionSummary(itemId);
                });
            }, 100);
        });

        // 标题修改跟踪系统函数

        /**
         * 添加或更新标题修改
         * @param {string} itemId - 条目ID
         * @param {string} originalTitle - 原始标题
         * @param {string} editedTitle - 编辑后的标题
         */
        function trackTitleModification(itemId, originalTitle, editedTitle) {
            // 如果编辑后的标题与原始标题相同，则删除修改记录
            if (originalTitle === editedTitle) {
                deleteTitleModification(itemId);
                return;
            }

            titleModifications[itemId] = {
                original: originalTitle,
                edited: editedTitle,
                timestamp: new Date().toISOString()
            };

            // 更新UI显示
            updateTitleDisplay(itemId);
            updateSyncButtonText();
        }

        /**
         * 删除标题修改记录
         * @param {string} itemId - 条目ID
         */
        function deleteTitleModification(itemId) {
            if (titleModifications[itemId]) {
                delete titleModifications[itemId];

                // 更新UI显示
                updateTitleDisplay(itemId);
                updateSyncButtonText();
            }
        }

        /**
         * 重置所有标题修改
         */
        function resetAllTitleModifications() {
            const itemIds = Object.keys(titleModifications);
            titleModifications = {};

            // 更新所有修改过的标题显示
            itemIds.forEach(itemId => {
                updateTitleDisplay(itemId);
            });

            updateSyncButtonText();
        }

        /**
         * 更新标题显示（添加或移除修改标记）
         * @param {string} itemId - 条目ID
         */
        function updateTitleDisplay(itemId) {
            const titleElement = document.querySelector(`.card-title[data-item-id="${itemId}"]`);
            if (!titleElement) return;

            const isModified = !!titleModifications[itemId];

            // 更新修改标记
            if (isModified) {
                titleElement.classList.add('title-modified');

                // 更新显示的标题文本
                const titleTextElement = titleElement.querySelector('.title-text');
                if (titleTextElement) {
                    titleTextElement.textContent = titleModifications[itemId].edited;
                }

                // 更新工具提示
                const tooltipElement = titleElement.querySelector('.title-tooltip');
                if (tooltipElement) {
                    tooltipElement.textContent = `原标题: ${titleModifications[itemId].original}`;
                    tooltipElement.style.display = 'block'; // 确保工具提示可见

                    // 添加高亮样式
                    tooltipElement.style.background = '#ff6b6b';
                    tooltipElement.style.fontWeight = 'bold';
                }

                // 显示重置按钮
                const resetBtn = titleElement.querySelector('.title-reset-btn');
                if (resetBtn) {
                    resetBtn.style.display = 'inline-block';
                }
            } else {
                titleElement.classList.remove('title-modified');

                // 恢复原始标题
                const titleTextElement = titleElement.querySelector('.title-text');
                const originalTitle = titleElement.getAttribute('data-original-title');
                if (titleTextElement && originalTitle) {
                    titleTextElement.textContent = originalTitle;
                }

                // 更新工具提示
                const tooltipElement = titleElement.querySelector('.title-tooltip');
                if (tooltipElement) {
                    tooltipElement.textContent = '点击编辑标题';
                    tooltipElement.style.display = 'none'; // 默认隐藏

                    // 恢复默认样式
                    tooltipElement.style.background = '#2d3748';
                    tooltipElement.style.fontWeight = 'normal';
                }

                // 隐藏重置按钮
                const resetBtn = titleElement.querySelector('.title-reset-btn');
                if (resetBtn) {
                    resetBtn.style.display = 'none';
                }
            }
        }

        /**
         * 获取修改的标题数量
         * @returns {number} 修改的标题数量
         */
        function getModifiedTitlesCount() {
            return Object.keys(titleModifications).length;
        }

        /**
         * 更新同步按钮文本，显示修改的标题和短剧名数量
         */
        function updateSyncButtonText() {
            const syncBtn = document.getElementById('syncBtn');
            if (!syncBtn) return;

            const modifiedTitlesCount = getModifiedTitlesCount();
            const modifiedShortNamesCount = getModifiedShortNamesCount();
            const totalModified = modifiedTitlesCount + modifiedShortNamesCount;

            if (totalModified > 0) {
                let text = '🔄 同步选中数据 (';
                const parts = [];
                if (modifiedTitlesCount > 0) {
                    parts.push(`${modifiedTitlesCount}个标题`);
                }
                if (modifiedShortNamesCount > 0) {
                    parts.push(`${modifiedShortNamesCount}个短剧名`);
                }
                text += parts.join('、') + '已修改)';
                syncBtn.textContent = text;
            } else {
                syncBtn.textContent = '🔄 同步选中数据';
            }
        }

        /**
         * 获取标题修改数据，用于同步请求
         * @param {boolean} selectedOnly - 是否只包含选中的条目
         * @returns {Object} 标题修改数据，格式为 {itemId: editedTitle}
         */
        function getTitleUpdates(selectedOnly = true) {
            const updates = {};

            for (const [itemId, modification] of Object.entries(titleModifications)) {
                // 如果只包含选中的条目，则检查条目是否被选中
                if (selectedOnly && !selectedIds.has(itemId)) {
                    continue;
                }

                updates[itemId] = modification.edited;
            }

            return updates;
        }

        // ========== 短剧名修改跟踪系统 ==========

        /**
         * 添加或更新短剧名修改
         * @param {string} itemId - 条目ID
         * @param {string} originalShortName - 原始短剧名
         * @param {string} editedShortName - 编辑后的短剧名
         */
        function trackShortNameModification(itemId, originalShortName, editedShortName) {
            // 如果编辑后的短剧名与原始短剧名相同，则删除修改记录
            if (originalShortName === editedShortName) {
                deleteShortNameModification(itemId);
                return;
            }

            shortNameModifications[itemId] = {
                original: originalShortName,
                edited: editedShortName,
                timestamp: new Date().toISOString()
            };

            // 更新UI显示
            updateShortNameDisplay(itemId);
            updateSyncButtonText();
        }

        /**
         * 删除短剧名修改记录
         * @param {string} itemId - 条目ID
         */
        function deleteShortNameModification(itemId) {
            if (shortNameModifications[itemId]) {
                delete shortNameModifications[itemId];

                // 更新UI显示
                updateShortNameDisplay(itemId);
                updateSyncButtonText();
            }
        }

        /**
         * 重置所有短剧名修改
         */
        function resetAllShortNameModifications() {
            const itemIds = Object.keys(shortNameModifications);
            shortNameModifications = {};

            // 更新所有修改过的短剧名显示
            itemIds.forEach(itemId => {
                updateShortNameDisplay(itemId);
            });

            updateSyncButtonText();
        }

        /**
         * 更新短剧名显示（添加或移除修改标记）
         * @param {string} itemId - 条目ID
         */
        function updateShortNameDisplay(itemId) {
            const shortNameElement = document.querySelector(`.card-short-name[data-item-id="${itemId}"]`);
            if (!shortNameElement) return;

            const isModified = !!shortNameModifications[itemId];
            const shortNameTextElement = shortNameElement.querySelector('.short-name-text');

            // 更新修改标记
            if (isModified) {
                shortNameTextElement.classList.add('short-name-modified');

                // 更新显示的短剧名文本
                if (shortNameTextElement) {
                    shortNameTextElement.textContent = shortNameModifications[itemId].edited;
                }

                // 显示重置按钮
                let resetBtn = shortNameElement.querySelector('.short-name-reset-btn');
                if (!resetBtn) {
                    resetBtn = document.createElement('button');
                    resetBtn.className = 'short-name-action-btn short-name-reset-btn';
                    resetBtn.title = '重置短剧名';
                    resetBtn.innerHTML = '↺';
                    resetBtn.addEventListener('click', function (e) {
                        e.stopPropagation();
                        resetShortNameById(itemId);
                    });
                    shortNameElement.querySelector('.short-name-actions').appendChild(resetBtn);
                }
            } else {
                shortNameTextElement.classList.remove('short-name-modified');

                // 恢复原始短剧名
                const originalShortName = shortNameElement.getAttribute('data-original-short-name');
                if (shortNameTextElement && originalShortName) {
                    shortNameTextElement.textContent = originalShortName;
                }

                // 移除重置按钮
                const resetBtn = shortNameElement.querySelector('.short-name-reset-btn');
                if (resetBtn) {
                    resetBtn.remove();
                }
            }
        }

        /**
         * 根据ID重置短剧名
         * @param {string} itemId - 条目ID
         */
        function resetShortNameById(itemId) {
            deleteShortNameModification(itemId);
        }

        /**
         * 获取修改的短剧名数量
         * @returns {number} 修改的短剧名数量
         */
        function getModifiedShortNamesCount() {
            return Object.keys(shortNameModifications).length;
        }

        /**
         * 获取短剧名修改数据，用于同步请求
         * @param {boolean} selectedOnly - 是否只包含选中的条目
         * @returns {Object} 短剧名修改数据，格式为 {itemId: editedShortName}
         */
        function getShortNameUpdates(selectedOnly = true) {
            const updates = {};

            for (const [itemId, modification] of Object.entries(shortNameModifications)) {
                // 如果只包含选中的条目，则检查条目是否被选中
                if (selectedOnly && !selectedIds.has(itemId)) {
                    continue;
                }

                updates[itemId] = modification.edited;
            }

            return updates;
        }

        /**
         * 删除选中的数据记录
         */
        function deleteSelectedData() {
            if (selectedIds.size === 0) {
                alert('请选择要删除的记录');
                return;
            }

            // 显示确认对话框
            const confirmMessage = `确定要删除选中的 ${selectedIds.size} 条记录吗？\n\n注意：此操作不可撤销，建议删除前先进行同步备份。`;
            if (!confirm(confirmMessage)) {
                return;
            }

            const deleteBtn = document.getElementById('deleteBtn');
            deleteBtn.disabled = true;
            deleteBtn.textContent = '🗑️ 删除中...';

            try {
                // 准备请求数据
                const requestData = {
                    selected_ids: Array.from(selectedIds)
                };

                fetch('/crawler/delete_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const deletedCount = data.deleted_count || 0;
                            const remainingCount = data.remaining_count || 0;

                            alert(`删除完成！\n删除了 ${deletedCount} 条记录\n剩余 ${remainingCount} 条记录`);

                            // 删除成功后重新加载数据
                            loadData();

                            // 清空选择
                            selectedIds.clear();
                            document.getElementById('selectAll').checked = false;
                            updateSelectionInfo();
                        } else {
                            alert('删除失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('删除失败: ' + error.message);
                    })
                    .finally(() => {
                        deleteBtn.disabled = false;
                        deleteBtn.textContent = '🗑️ 删除选中记录';
                        updateSyncButton();
                    });
            } catch (error) {
                console.error('删除请求准备失败:', error);
                alert('删除请求准备失败: ' + error.message);

                deleteBtn.disabled = false;
                deleteBtn.textContent = '🗑️ 删除选中记录';
                updateSyncButton();
            }
        }

        /**
         * 同步选中数据到服务端
         */
        function syncSelectedData() {
            if (selectedIds.size === 0) {
                alert('请选择要同步的记录');
                return;
            }

            const syncBtn = document.getElementById('syncBtn');
            syncBtn.disabled = true;
            syncBtn.textContent = '🔄 同步中...';

            try {
                // 准备请求数据
                const requestData = {
                    selected_ids: Array.from(selectedIds)
                };

                // 如果有标题修改，添加到请求中
                const titleUpdates = getTitleUpdates(true); // 只包含选中的条目
                if (Object.keys(titleUpdates).length > 0) {
                    requestData.title_updates = titleUpdates;
                }

                // 如果有短剧名修改，添加到请求中
                const shortNameUpdates = getShortNameUpdates(true); // 只包含选中的条目
                if (Object.keys(shortNameUpdates).length > 0) {
                    requestData.short_name_updates = shortNameUpdates;
                }

                fetch('/crawler/sync/sync_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const successCount = data.success_count || 0;
                            const failedCount = data.failed_count || 0;
                            const skippedCount = data.skipped_count || 0;
                            const titleUpdatesApplied = data.title_updates_applied || 0;
                            const shortNameUpdatesApplied = data.short_name_updates_applied || 0;

                            let message = `同步完成！\n成功: ${successCount} 条`;
                            if (skippedCount > 0) {
                                message += `\n跳过: ${skippedCount} 条（已存在）`;
                            }
                            if (failedCount > 0) {
                                message += `\n失败: ${failedCount} 条`;
                            }
                            if (titleUpdatesApplied > 0) {
                                message += `\n应用了 ${titleUpdatesApplied} 个标题修改`;
                            }
                            if (shortNameUpdatesApplied > 0) {
                                message += `\n应用了 ${shortNameUpdatesApplied} 个短剧名修改`;
                            }

                            alert(message);

                            // 同步成功后清空标题修改记录
                            titleModifications = {};

                            // 重新加载数据以更新同步状态和标题
                            loadData();

                            // 清空选择
                            selectedIds.clear();
                            document.getElementById('selectAll').checked = false;
                            updateSelectionInfo();
                        } else {
                            alert('同步失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('同步失败: ' + error.message);
                    })
                    .finally(() => {
                        syncBtn.disabled = false;
                        syncBtn.textContent = '🔄 同步选中数据';
                        updateSyncButton();
                        updateSyncButtonText();
                    });
            } catch (error) {
                console.error('同步请求准备失败:', error);
                alert('同步请求准备失败: ' + error.message);

                syncBtn.disabled = false;
                syncBtn.textContent = '🔄 同步选中数据';
                updateSyncButton();
                updateSyncButtonText();
            }
        }

        // 页面关闭时清理
        window.addEventListener('beforeunload', function () {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
        });
    </script>
</body>

</html>