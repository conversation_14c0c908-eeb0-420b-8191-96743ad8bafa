#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫客户端配置文件
"""

import os

# 爬虫配置
CRAWLER_CONFIG = {
    "default_url": "https://search.damai.cn/search.htm?spm=a2oeg.home.category.ditem_1.2f9423e1NcYicv&ctl=%E8%AF%9D%E5%89%A7%E6%AD%8C%E5%89%A7&order=1&cty=%E5%8C%97%E4%BA%AC",
    "default_max_items": 20,
    "default_delay": 2.0,
    "use_dynamic_loading": True,
    "max_load_attempts": 5,
}

# 数据存储配置
DATA_CONFIG = {
    "crawl_data_path": os.path.join(os.path.dirname(__file__), "crawl_data"),
    "details_file": "details_data.json",
    "crawled_ids_file": "crawled_ids.json",
    "summary_file": "crawl_summary.json",
}

# 同步服务端配置
SYNC_SERVER_CONFIG = {
    "base_url": "http://localhost:8112",  # 同步服务端地址（容器内部）
    "api_key": "crawl4ai_sync_2024",  # API密钥
    "timeout": 30,  # 请求超时时间
    "retry_times": 3,  # 重试次数
}

# 客户端Web服务配置
CLIENT_SERVER_CONFIG = {"host": "0.0.0.0", "port": 5000, "debug": True}

# Playwright浏览器配置
PLAYWRIGHT_CONFIG = {
    "headless": False,  # 是否无头模式
    "slow_mo": 500,  # 操作延迟（毫秒）
    "timeout": 30000,  # 超时时间（毫秒）
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "viewport": {"width": 1920, "height": 1080},
    "locale": "zh-CN",
    "timezone_id": "Asia/Shanghai",
}

# 确保目录存在
os.makedirs(DATA_CONFIG["crawl_data_path"], exist_ok=True)
