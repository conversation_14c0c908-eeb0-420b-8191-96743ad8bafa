#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫客户端配置文件
"""

import os

# 爬虫配置
CRAWLER_CONFIG = {
    "default_url": "https://search.damai.cn/search.htm?spm=a2oeg.home.category.ditem_1.2f9423e1NcYicv&ctl=%E8%AF%9D%E5%89%A7%E6%AD%8C%E5%89%A7&order=1&cty=%E5%8C%97%E4%BA%AC",
    "default_max_items": 20,
    "default_delay": 2.0,
    "use_dynamic_loading": True,
    "max_load_attempts": 5,
}

# 数据存储配置
DATA_CONFIG = {
    "crawl_data_path": os.path.join(os.path.dirname(__file__), "crawl_data"),
    "details_file": "details_data.json",
    "crawled_ids_file": "crawled_ids.json",
    "summary_file": "crawl_summary.json",
}

# 数据库配置 - 支持环境变量
DATABASE_CONFIG = {
    "host": os.environ.get("DB_HOST", "*************"),
    "port": int(os.environ.get("DB_PORT", 3306)),
    "user": os.environ.get("DB_USER", "root"),
    "password": os.environ.get("DB_PASSWORD", "VYw^%jk6U^"),
    "database": os.environ.get("DB_DATABASE", "digital_collection"),
}

# 海报下载配置
POSTER_DOWNLOAD_PATH = os.path.join(os.path.dirname(__file__), "downloads", "posters")
DOWNLOAD_CONFIG = {
    "timeout": 30,  # 下载超时时间（秒）
    "retry_times": 3,  # 重试次数
    "headers": {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    },
}

# 客户端Web服务配置
CLIENT_SERVER_CONFIG = {"host": "0.0.0.0", "port": 5000, "debug": True}

# Playwright浏览器配置
PLAYWRIGHT_CONFIG = {
    "headless": False,  # 是否无头模式
    "slow_mo": 500,  # 操作延迟（毫秒）
    "timeout": 30000,  # 超时时间（毫秒）
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "viewport": {"width": 1920, "height": 1080},
    "locale": "zh-CN",
    "timezone_id": "Asia/Shanghai",
}

# 确保目录存在
os.makedirs(DATA_CONFIG["crawl_data_path"], exist_ok=True)
