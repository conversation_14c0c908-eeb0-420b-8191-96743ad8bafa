# Crawl4AI 爬虫客户端

## 概述

Crawl4AI爬虫客户端是一个独立的数据爬取工具，提供Web界面和Python脚本，支持：
- 从大麦网爬取演出数据
- 本地数据管理和展示
- 与同步服务端通信
- 数据上传和同步功能

## 功能特性

- ✅ **数据爬取**: 支持大麦网演出信息爬取
- ✅ **动态加载**: 处理JavaScript动态内容
- ✅ **数据管理**: 本地数据存储和管理
- ✅ **Web界面**: 友好的操作界面
- ✅ **服务端通信**: 与同步服务端无缝对接
- ✅ **批量操作**: 支持批量上传和同步

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置设置

编辑 `config.py` 文件，修改同步服务端地址：

```python
SYNC_SERVER_CONFIG = {
    'base_url': 'http://your_sync_server:8080/api/v1',
    'api_key': 'crawl4ai_sync_2024',
    'timeout': 30,
    'retry_times': 3
}
```

### 3. 启动客户端

```bash
python client.py
```

客户端将在 `http://localhost:5000` 启动。

## 使用指南

### 1. 数据爬取

#### Web界面操作
1. 访问 `http://localhost:5000`
2. 配置爬取参数：
   - 目标URL（可使用默认）
   - 最大爬取数量
   - 请求延迟时间
   - 动态加载设置
3. 点击"开始爬取"
4. 实时查看爬取进度

#### 爬取配置

```python
CRAWLER_CONFIG = {
    'default_url': "https://search.damai.cn/search.htm...",
    'default_max_items': 20,
    'default_delay': 2.0,
    'use_dynamic_loading': True,
    'max_load_attempts': 5
}
```

### 2. 数据管理

#### 查看本地数据
- 在Web界面中查看已爬取的数据
- 支持数据筛选和搜索
- 显示数据统计信息

#### 数据存储位置
```
crawl_data/
├── details_data.json      # 详细数据
├── crawled_ids.json      # 已爬取ID列表
└── crawl_summary.json    # 爬取摘要
```

### 3. 与服务端通信


#### 上传数据
1. 选择要上传的记录
2. 点击"上传到服务端"
3. 查看上传结果

#### 执行同步
1. 从服务端获取数据列表
2. 选择要同步的记录
3. 执行数据库同步操作

## API接口

客户端提供以下API接口：

### 爬虫控制
- `POST /start_crawl` - 开始爬取
- `POST /stop_crawl` - 停止爬取
- `GET /status` - 获取爬取状态
- `GET /data` - 获取本地数据

### 同步功能
- `POST /sync/upload_data` - 上传数据到服务端
- `GET /sync/server_data` - 获取服务端数据
- `GET /sync/server_statistics` - 获取服务端统计
- `POST /sync/execute` - 执行同步操作

### 配置管理
- `GET /config` - 获取客户端配置

## 配置说明

### 爬虫配置

```python
CRAWLER_CONFIG = {
    'default_url': "目标网站URL",
    'default_max_items': 20,        # 默认爬取数量
    'default_delay': 2.0,           # 默认延迟时间
    'use_dynamic_loading': True,    # 启用动态加载
    'max_load_attempts': 5          # 最大加载尝试次数
}
```

### 数据存储配置

```python
DATA_CONFIG = {
    'crawl_data_path': './crawl_data',
    'details_file': 'details_data.json',
    'crawled_ids_file': 'crawled_ids.json',
    'summary_file': 'crawl_summary.json'
}
```

### 客户端服务配置

```python
CLIENT_SERVER_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': True
}
```

## 使用示例

### 1. 命令行爬取

```python
from deep_crawler import DeepCrawler
import asyncio

async def crawl_example():
    crawler = DeepCrawler("https://search.damai.cn/...")
    await crawler.run_deep_crawl(max_items=10, delay_between_requests=2.0)

asyncio.run(crawl_example())
```

### 2. 与服务端交互

```python
from sync_client import SyncServerClient

# 创建客户端
client = SyncServerClient()


# 上传数据
data = [{"id": "123", "title": "演出名称", ...}]
result = client.upload_data(data)
print(result)

# 执行同步
result = client.sync_data(["123", "456"])
print(result)
```

## 故障排除

### 常见问题

1. **爬取失败**
   - 检查网络连接
   - 验证目标网站可访问性
   - 调整延迟时间和重试设置

2. **动态加载问题**
   - 确保已安装Chrome浏览器
   - 检查webdriver版本
   - 增加加载等待时间

3. **服务端连接失败**
   - 检查服务端是否启动
   - 验证网络连通性
   - 确认API密钥正确

4. **数据保存失败**
   - 检查磁盘空间
   - 验证文件权限
   - 确认目录存在

### 日志查看

客户端日志会在控制台输出，包含：
- 爬取进度信息
- 错误和异常信息
- 服务端通信日志

### 性能优化

1. **爬取性能**
   - 适当调整延迟时间
   - 合理设置并发数量
   - 使用动态加载优化

2. **内存优化**
   - 定期清理临时数据
   - 分批处理大量数据
   - 监控内存使用

## 部署建议

### 1. 开发环境

```bash
# 克隆代码
git clone <repository>
cd crawler-client

# 安装依赖
pip install -r requirements.txt

# 启动服务
python client.py
```

### 2. 生产环境

```bash
# 使用gunicorn部署
pip install gunicorn
gunicorn -w 2 -b 0.0.0.0:5000 client:app
```

### 3. Docker部署

```dockerfile
FROM python:3.9-slim

# 安装Chrome和相关依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "client.py"]
```

## 安全注意事项

1. **网络安全**
   - 使用HTTPS连接服务端
   - 保护API密钥安全
   - 限制网络访问范围

2. **数据安全**
   - 定期备份爬取数据
   - 清理敏感信息
   - 监控异常访问

3. **爬取合规**
   - 遵守网站robots.txt
   - 合理设置请求频率
   - 避免对目标网站造成负担

## 支持和维护

### 更新依赖

```bash
pip install --upgrade -r requirements.txt
```

### 数据备份

```bash
# 备份爬取数据
tar -czf crawl_data_backup.tar.gz crawl_data/
```

### 监控运行状态

- 定期检查爬取状态
- 监控服务端连接
- 查看错误日志 