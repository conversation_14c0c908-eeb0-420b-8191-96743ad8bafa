#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Crawl4AI 爬虫客户端
提供数据爬取功能和同步服务端通信
"""

import json
import os
import re
import threading
from datetime import datetime

from config import CLIENT_SERVER_CONFIG, CRAWLER_CONFIG, DATA_CONFIG, DATABASE_CONFIG
from deep_crawler import DeepCrawler
from flask import Flask, jsonify, render_template, request
from local_database_sync import local_sync_service

app = Flask(__name__)

# Flask应用配置
app.config["JSON_AS_ASCII"] = False  # 支持中文
app.config["JSONIFY_PRETTYPRINT_REGULAR"] = True
app.config["TRAP_HTTP_EXCEPTIONS"] = True

# 全局变量
crawler_instance = None

crawler_status = {
    "running": False,
    "progress": 0,
    "total": 0,
    "current_item": "",
    "message": "准备就绪",
    "logs": [],
}

# 同步状态文件路径
SYNC_STATUS_FILE = os.path.join(DATA_CONFIG["crawl_data_path"], "sync_status.json")


# 添加全局错误处理器
@app.errorhandler(Exception)
def handle_exception(e):
    """全局异常处理器，确保返回JSON而不是HTML"""
    import traceback

    error_msg = f"服务器内部错误: {str(e)}"
    print(f"错误详情: {traceback.format_exc()}")

    # 如果是AJAX请求，返回JSON
    if (
        request.is_json
        or request.path.startswith("/start_crawl")
        or request.path.startswith("/status")
    ):
        return jsonify({"success": False, "message": error_msg}), 500

    # 否则返回错误页面
    return f"<h1>服务器错误</h1><p>{error_msg}</p>", 500


@app.errorhandler(404)
def handle_404(_):
    """404错误处理器"""
    if request.is_json or "/api/" in request.path:
        return jsonify({"success": False, "message": "接口不存在"}), 404
    return render_template("index.html"), 404


def load_local_sync_status():
    """加载本地同步状态"""
    try:
        if os.path.exists(SYNC_STATUS_FILE):
            with open(SYNC_STATUS_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"加载同步状态失败: {e}")
        return {}


def save_local_sync_status(sync_status):
    """保存本地同步状态"""
    try:
        os.makedirs(os.path.dirname(SYNC_STATUS_FILE), exist_ok=True)
        with open(SYNC_STATUS_FILE, "w", encoding="utf-8") as f:
            json.dump(sync_status, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存同步状态失败: {e}")
        return False


def add_log(message):
    """添加日志信息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    crawler_status["logs"].append(log_entry)
    # 保持最新的50条日志
    if len(crawler_status["logs"]) > 50:
        crawler_status["logs"] = crawler_status["logs"][-50:]
    print(log_entry)


def load_crawled_data():
    """加载已爬取的数据"""
    details_file = os.path.join(
        DATA_CONFIG["crawl_data_path"], DATA_CONFIG["details_file"]
    )
    if os.path.exists(details_file):
        try:
            with open(details_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                add_log(f"加载了 {len(data)} 条本地数据")
                return data
        except Exception as e:
            add_log(f"加载数据失败: {e}")
            return []
    return []


def status_update_callback(progress=None, total=None, current_item=None, message=None):
    """状态更新回调函数"""
    global crawler_status
    if progress is not None:
        crawler_status["progress"] = progress
    if total is not None:
        crawler_status["total"] = total
    if current_item is not None:
        crawler_status["current_item"] = current_item
    if message is not None:
        crawler_status["message"] = message


def run_crawler_thread(
    base_url, max_items, delay, max_pages=10, city="", category="", time_period=""
):
    """在新线程中运行爬虫（不使用multiprocessing）"""
    try:
        # 更新全局状态
        global crawler_status, crawler_instance
        crawler_status["running"] = True
        crawler_status["message"] = "爬虫正在运行"

        # 直接在当前线程中初始化爬虫
        add_log("开始初始化爬虫")
        crawler_instance = DeepCrawler(base_url, DATA_CONFIG["crawl_data_path"])

        # 设置状态回调
        crawler_instance.set_status_callback(status_update_callback)

        # 设置过滤条件
        if city or category or time_period:
            add_log(f"设置过滤条件: 城市={city}, 分类={category}, 时间={time_period}")
            crawler_instance.set_filters(
                city=city, category=category, time_period=time_period
            )

        add_log("开始使用 Playwright 可视化点击分页爬取")
        try:
            crawler_instance.run_deep_crawl(
                max_items=max_items,
                max_pages=max_pages,
                delay_between_requests=delay,
                headless=True,
            )
            add_log("爬取任务完成")
        except Exception as e:
            import traceback

            error_details = traceback.format_exc()

            # 检查是否是线程切换错误
            if "Cannot switch to a different thread" in str(e):
                add_log(f"爬取过程中出现线程切换警告 (可以忽略): {e}")
                # 尝试继续执行，因为这通常不会影响主要功能
            else:
                add_log(f"爬取过程中出错: {e}")
                add_log(f"错误详情: {error_details}")

            # 确保浏览器被关闭
            try:
                crawler_instance.close_playwright_browser()
                add_log("已尝试关闭浏览器")
            except Exception as close_error:
                if "Cannot switch to a different thread" in str(close_error):
                    add_log("关闭浏览器时出现线程切换警告 (可以忽略)")
                else:
                    add_log(f"关闭浏览器时出错: {close_error}")

        # 更新状态
        crawler_status["running"] = False
        crawler_status["message"] = "爬取任务完成"
        add_log("爬取线程结束")
    except Exception as e:
        # 更新状态
        crawler_status["running"] = False
        crawler_status["message"] = f"爬取失败: {str(e)}"
        add_log(f"爬取失败: {e}")
        import traceback

        add_log(f"错误详情: {traceback.format_exc()}")


# ==================== Web 路由 ====================


@app.route("/")
def index():
    """主页"""
    return render_template("index.html")


@app.route("/crawler/")
def crawler_index():
    """爬虫页面（带前缀）"""
    return render_template("index.html")


@app.route("/crawler/start_crawl", methods=["POST"])
def start_crawl():
    """开始爬取"""
    try:
        print(f"收到爬取请求，当前状态: {crawler_status['running']}")

        if crawler_status["running"]:
            return jsonify({"success": False, "message": "爬虫正在运行中"})

        # 检查请求数据
        if not request.is_json:
            print("错误: 请求不是JSON格式")
            return jsonify({"success": False, "message": "请求格式错误，需要JSON数据"})

        data = request.get_json()
        if data is None:
            print("错误: 无法解析JSON数据")
            return jsonify({"success": False, "message": "JSON数据解析失败"})

        print(f"解析的数据: {data}")

        # 获取参数，提供默认值
        base_url = data.get("url", "").strip()
        city = data.get("city", "").strip()
        category = data.get("category", "").strip()
        time_period = data.get("time_period", "").strip()
        max_items = int(
            data.get("max_items", CRAWLER_CONFIG.get("default_max_items", 20))
        )
        delay = float(data.get("delay", CRAWLER_CONFIG.get("default_delay", 2.0)))
        max_pages = int(data.get("max_pages", 10))

        if not base_url:
            base_url = CRAWLER_CONFIG.get("default_url", "")
            if not base_url:
                return jsonify(
                    {"success": False, "message": "未提供URL且无默认URL配置"}
                )

        print(
            f"解析参数: URL={base_url}, city={city}, category={category}, time_period={time_period}, max_items={max_items}, delay={delay}, max_pages={max_pages}"
        )

        # 验证参数
        if max_items < 1 or max_items > 100:
            return jsonify({"success": False, "message": "最大爬取数量必须在1-100之间"})

        if delay < 0.5 or delay > 10:
            return jsonify({"success": False, "message": "请求间隔必须在0.5-10秒之间"})

        if max_pages < 1 or max_pages > 50:
            return jsonify({"success": False, "message": "最大页数必须在1-50之间"})

        # 重置状态
        crawler_status["progress"] = 0
        crawler_status["total"] = max_items
        crawler_status["current_item"] = ""
        crawler_status["logs"] = []

        # 构建过滤条件日志信息
        filter_info = []
        if city:
            filter_info.append(f"城市={city}")
        if category:
            filter_info.append(f"分类={category}")
        if time_period:
            filter_info.append(f"时间={time_period}")

        filter_msg = f", 过滤条件: {', '.join(filter_info)}" if filter_info else ""
        add_log(
            f"开始爬取任务: URL={base_url}, 最大数量={max_items}, 延迟={delay}秒{filter_msg}"
        )
        add_log(f"🤖 使用Selenium可视化点击分页，适用于大麦网，最大页数: {max_pages}")

        # 在新线程中启动爬虫
        thread = threading.Thread(
            target=run_crawler_thread,
            args=(base_url, max_items, delay, max_pages, city, category, time_period),
        )
        thread.daemon = True
        thread.start()

        print("爬虫线程已启动")
        return jsonify({"success": True, "message": "爬取任务已开始"})

    except Exception as e:
        import traceback

        error_detail = traceback.format_exc()
        print(f"start_crawl 错误: {error_detail}")
        return jsonify({"success": False, "message": f"启动失败: {str(e)}"})


@app.route("/crawler/status")
def get_status():
    """获取爬虫状态"""
    try:
        global crawler_instance

        # 获取爬虫日志
        crawler_logs = []
        if crawler_instance:
            try:
                crawler_logs = crawler_instance.get_logs()
            except Exception as e:
                print(f"获取爬虫日志失败: {e}")
                crawler_logs = []

        # 合并系统日志和爬虫日志
        all_logs = crawler_status["logs"] + crawler_logs
        # 保持最新的100条日志
        if len(all_logs) > 100:
            all_logs = all_logs[-100:]

        return jsonify(
            {
                "running": crawler_status["running"],
                "progress": crawler_status["progress"],
                "total": crawler_status["total"],
                "current_item": crawler_status["current_item"],
                "message": crawler_status["message"],
                "logs": all_logs,
            }
        )
    except Exception as e:
        import traceback

        print(f"get_status 错误: {traceback.format_exc()}")
        return jsonify(
            {
                "running": False,
                "progress": 0,
                "total": 0,
                "current_item": "",
                "message": f"状态获取失败: {str(e)}",
                "logs": [f"错误: {str(e)}"],
            }
        )


@app.route("/crawler/data")
def get_data():
    """获取已爬取的数据"""
    global crawler_instance, crawler_status

    # 如果爬虫正在运行且实例存在，优先返回内存数据
    if crawler_status.get("running") and crawler_instance:
        data = crawler_instance.details_data
    else:
        # 否则从文件加载
        data = load_crawled_data()
    return jsonify({"total": len(data), "items": data})


@app.route("/crawler/stop_crawl", methods=["POST"])
def stop_crawl():
    """停止爬取"""
    global crawler_status, crawler_instance
    if crawler_status["running"]:
        crawler_status["running"] = False
        crawler_status["message"] = "用户手动停止"
        add_log("用户手动停止爬取")

        # 如果爬虫实例存在，调用停止方法
        if crawler_instance:
            crawler_instance.stop()

        return jsonify({"success": True, "message": "爬取已停止"})
    return jsonify({"success": False, "message": "爬虫未在运行"})


# ==================== 同步功能路由 ====================


@app.route("/crawler/sync/local_statistics")
def get_local_statistics():
    """获取本地同步统计"""
    try:
        statistics = local_sync_service.get_sync_statistics()
        return jsonify(
            {
                "success": True,
                "statistics": statistics,
                "message": "获取本地同步统计成功",
            }
        )
    except Exception as e:
        return jsonify({"success": False, "message": f"获取本地同步统计失败: {str(e)}"})


@app.route("/crawler/sync/local_history")
def get_local_history():
    """获取本地同步历史"""
    try:
        history = local_sync_service.load_sync_history()
        return jsonify(
            {"success": True, "history": history, "message": "获取本地同步历史成功"}
        )
    except Exception as e:
        return jsonify({"success": False, "message": f"获取本地同步历史失败: {str(e)}"})


@app.route("/crawler/sync/local_data")
def get_local_data():
    """获取本地数据列表"""
    try:
        all_data = load_crawled_data()
        local_sync_status = load_local_sync_status()

        # 为每个数据项添加同步状态
        for item in all_data:
            item["is_synced"] = local_sync_status.get(item["id"], False)

        return jsonify(
            {
                "success": True,
                "data": all_data,
                "total": len(all_data),
                "message": "获取本地数据成功",
            }
        )
    except Exception as e:
        return jsonify({"success": False, "message": f"获取本地数据失败: {str(e)}"})


# 保留旧接口名称以兼容前端
@app.route("/crawler/sync/server_data")
def get_server_data():
    """获取服务端数据（重定向到本地数据）"""
    return get_local_data()


@app.route("/crawler/sync/server_statistics")
def get_server_statistics():
    """获取服务端同步统计（重定向到本地统计）"""
    return get_local_statistics()


def validate_title(title):
    """验证标题是否有效

    Args:
        title (str): 要验证的标题

    Returns:
        tuple: (是否有效, 错误消息)
    """
    if not isinstance(title, str):
        return False, "标题必须是字符串"

    if len(title) > 100:
        return False, "标题长度不能超过100个字符"

    # 检查特殊字符
    import re

    if re.search(r"[<>]", title):
        return False, "标题不能包含 < 或 > 字符"

    return True, ""


def apply_title_updates(data_items, title_updates):
    """应用标题更新到数据项

    Args:
        data_items (list): 数据项列表
        title_updates (dict): 标题更新字典，格式为 {item_id: new_title}

    Returns:
        tuple: (更新后的数据项列表, 应用的更新数量)
    """
    if not title_updates:
        return data_items, 0

    updates_applied = 0
    invalid_updates = []

    # 创建ID到数据项的映射，以便快速查找
    id_to_item = {item["id"]: item for item in data_items}

    # 应用标题更新
    for item_id, new_title in title_updates.items():
        if item_id in id_to_item:
            # 验证标题
            is_valid, error_msg = validate_title(new_title)
            if not is_valid:
                add_log(f"标题验证失败: ID={item_id}, 错误='{error_msg}'")
                invalid_updates.append((item_id, error_msg))
                continue

            # 记录原始标题（用于日志）
            original_title = id_to_item[item_id].get("title", "未知标题")

            try:
                # 更新标题
                id_to_item[item_id]["title"] = new_title
                updates_applied += 1

                add_log(
                    f"更新标题: ID={item_id}, 原标题='{original_title}', 新标题='{new_title}'"
                )
            except Exception as e:
                add_log(f"更新标题失败: ID={item_id}, 错误='{str(e)}'")
                invalid_updates.append((item_id, str(e)))

    # 如果有无效更新，记录日志
    if invalid_updates:
        add_log(f"有 {len(invalid_updates)} 个标题更新无效: {invalid_updates}")

    return data_items, updates_applied


def apply_short_name_updates(data_items, short_name_updates):
    """
    应用短剧名更新到数据项

    Args:
        data_items: 数据项列表
        short_name_updates: 短剧名更新字典，格式为 {item_id: new_short_name}

    Returns:
        tuple: (更新后的数据项列表, 成功应用的更新数量)
    """
    updates_applied = 0
    invalid_updates = []

    # 创建ID到数据项的映射，以便快速查找
    id_to_item = {item["id"]: item for item in data_items}

    # 应用短剧名更新
    for item_id, new_short_name in short_name_updates.items():
        if item_id in id_to_item:
            # 验证短剧名
            is_valid, error_msg = validate_short_name(new_short_name)
            if not is_valid:
                add_log(f"短剧名验证失败: ID={item_id}, 错误='{error_msg}'")
                invalid_updates.append((item_id, error_msg))
                continue

            # 记录原始短剧名（用于日志）
            original_short_name = id_to_item[item_id].get("shortName", "未知短剧名")

            try:
                # 更新短剧名
                id_to_item[item_id]["shortName"] = new_short_name
                updates_applied += 1

                add_log(
                    f"更新短剧名: ID={item_id}, 原短剧名='{original_short_name}', 新短剧名='{new_short_name}'"
                )
            except Exception as e:
                add_log(f"更新短剧名失败: ID={item_id}, 错误='{str(e)}'")
                invalid_updates.append((item_id, str(e)))

    # 如果有无效更新，记录日志
    if invalid_updates:
        add_log(f"有 {len(invalid_updates)} 个短剧名更新无效: {invalid_updates}")

    return data_items, updates_applied


def validate_short_name(short_name):
    """
    验证短剧名

    Args:
        short_name: 要验证的短剧名

    Returns:
        tuple: (是否有效, 错误信息)
    """
    if not short_name or not isinstance(short_name, str):
        return False, "短剧名不能为空"

    short_name = short_name.strip()
    if not short_name:
        return False, "短剧名不能为空"

    if len(short_name) > 50:
        return False, "短剧名长度不能超过50个字符"

    # 检查特殊字符
    invalid_chars = re.search(r"[<>]", short_name)
    if invalid_chars:
        return False, "短剧名不能包含 < 或 > 字符"

    return True, ""


@app.route("/crawler/sync/sync_data", methods=["POST"])
def sync_data():
    """同步数据到服务端（一步完成：数据上传+数据库同步）"""
    try:
        data = request.get_json()
        selected_ids = data.get("selected_ids", [])
        title_updates = data.get("title_updates", {})
        short_name_updates = data.get("short_name_updates", {})

        if not selected_ids:
            return jsonify({"success": False, "message": "请选择要同步的记录"})

        add_log(f"开始一步式同步流程，共 {len(selected_ids)} 条记录")

        if title_updates:
            add_log(f"收到 {len(title_updates)} 个标题更新")

        if short_name_updates:
            add_log(f"收到 {len(short_name_updates)} 个短剧名更新")

        # 准备同步数据
        all_data = load_crawled_data()
        sync_data_list = []
        for item_id in selected_ids:
            for item in all_data:
                if item["id"] == item_id:
                    sync_data_list.append(item)
                    break

        if not sync_data_list:
            return jsonify({"success": False, "message": "没有找到要同步的数据"})

        # 应用标题更新
        title_updates_applied = 0
        if title_updates:
            sync_data_list, title_updates_applied = apply_title_updates(
                sync_data_list, title_updates
            )
            add_log(f"应用了 {title_updates_applied} 个标题更新")

        # 应用短剧名更新
        short_name_updates_applied = 0
        if short_name_updates:
            sync_data_list, short_name_updates_applied = apply_short_name_updates(
                sync_data_list, short_name_updates
            )
            add_log(f"应用了 {short_name_updates_applied} 个短剧名更新")

        # 直接同步数据到数据库（本地同步）
        add_log("执行本地数据库同步")

        # 格式化打印sync_data_list用于调试
        print(json.dumps(sync_data_list, ensure_ascii=False, indent=2))

        sync_result = local_sync_service.sync_data_to_database(sync_data_list)

        if sync_result.get("success"):
            add_log(f"同步成功: {sync_result.get('message', '同步完成')}")

            # 第三步：更新本地同步状态
            local_sync_status = load_local_sync_status()
            sync_result_data = sync_result.get("result", {})

            # 标记成功同步的记录
            for item_id in sync_result_data.get("success_ids", []):
                local_sync_status[item_id] = True

            # 标记跳过的记录也为已同步（因为数据库中已存在）
            for item_id in sync_result_data.get("skipped_ids", []):
                local_sync_status[item_id] = True

            save_local_sync_status(local_sync_status)
            add_log("本地同步状态已更新")

            # 如果应用了标题更新，更新本地数据
            if title_updates_applied > 0:
                try:
                    # 更新本地数据文件
                    details_file = os.path.join(
                        DATA_CONFIG["crawl_data_path"], DATA_CONFIG["details_file"]
                    )
                    if os.path.exists(details_file):
                        with open(details_file, "r", encoding="utf-8") as f:
                            local_data = json.load(f)

                        # 应用标题更新到本地数据
                        local_data, _ = apply_title_updates(local_data, title_updates)

                        # 保存更新后的数据
                        with open(details_file, "w", encoding="utf-8") as f:
                            json.dump(local_data, f, ensure_ascii=False, indent=2)

                        add_log("已更新本地数据文件中的标题")
                except Exception as e:
                    add_log(f"更新本地数据文件失败: {str(e)}")

            # 返回详细结果
            return jsonify(
                {
                    "success": True,
                    "message": sync_result.get("message", "同步完成"),
                    "success_count": sync_result_data.get("success", 0),
                    "failed_count": sync_result_data.get("failed", 0),
                    "skipped_count": sync_result_data.get("skipped", 0),
                    "title_updates_applied": title_updates_applied,
                    "short_name_updates_applied": short_name_updates_applied,
                    "result": sync_result_data,
                }
            )
        else:
            add_log(f"同步失败: {sync_result.get('message', '同步失败')}")
            return jsonify(
                {"success": False, "message": sync_result.get("message", "同步失败")}
            )

    except Exception as e:
        error_msg = f"同步过程发生错误: {str(e)}"
        add_log(error_msg)
        return jsonify({"success": False, "message": error_msg})


@app.route("/crawler/sync/sync_status")
def get_sync_status():
    """获取本地同步状态"""
    try:
        # 从本地获取同步状态
        local_sync_status = load_local_sync_status()

        return jsonify(
            {
                "success": True,
                "status": local_sync_status,
                "source": "local",
                "message": "从本地获取同步状态",
            }
        )
    except Exception as e:
        return jsonify(
            {"success": False, "status": {}, "message": f"获取同步状态失败: {str(e)}"}
        )


@app.route("/crawler/delete_data", methods=["POST"])
def delete_data():
    """删除本地爬取的数据记录"""
    try:
        data = request.get_json()
        selected_ids = data.get("selected_ids", [])

        if not selected_ids:
            return jsonify({"success": False, "message": "请选择要删除的记录"})

        add_log(f"开始删除 {len(selected_ids)} 条记录")

        # 加载当前数据
        details_file = os.path.join(
            DATA_CONFIG["crawl_data_path"], DATA_CONFIG["details_file"]
        )
        if not os.path.exists(details_file):
            return jsonify({"success": False, "message": "数据文件不存在"})

        with open(details_file, "r", encoding="utf-8") as f:
            all_data = json.load(f)

        # 过滤掉要删除的记录
        filtered_data = []
        deleted_count = 0
        deleted_ids = []

        for item in all_data:
            if item["id"] in selected_ids:
                deleted_count += 1
                deleted_ids.append(item["id"])
                add_log(f"删除记录: ID={item['id']}, 标题={item.get('title', '未知')}")
            else:
                filtered_data.append(item)

        if deleted_count == 0:
            return jsonify({"success": False, "message": "没有找到要删除的记录"})

        # 保存过滤后的数据
        with open(details_file, "w", encoding="utf-8") as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)

        # 同时清理本地同步状态
        local_sync_status = load_local_sync_status()
        for deleted_id in deleted_ids:
            if deleted_id in local_sync_status:
                del local_sync_status[deleted_id]
        save_local_sync_status(local_sync_status)

        add_log(
            f"删除完成: 删除了 {deleted_count} 条记录，剩余 {len(filtered_data)} 条记录"
        )

        return jsonify(
            {
                "success": True,
                "message": f"成功删除 {deleted_count} 条记录",
                "deleted_count": deleted_count,
                "remaining_count": len(filtered_data),
                "deleted_ids": deleted_ids,
            }
        )

    except Exception as e:
        error_msg = f"删除记录时发生错误: {str(e)}"
        add_log(error_msg)
        return jsonify({"success": False, "message": error_msg})


# ==================== 配置路由 ====================


@app.route("/crawler/config")
def get_config():
    """获取客户端配置"""
    return jsonify(
        {
            "crawler_config": CRAWLER_CONFIG,
            "database_config": {
                "host": DATABASE_CONFIG["host"],
                "port": DATABASE_CONFIG["port"],
                "database": DATABASE_CONFIG["database"],
                # 不返回用户名和密码
            },
        }
    )


@app.route("/health")
def health_check():
    """健康检查端点"""
    return jsonify(
        {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
        }
    ), 200


if __name__ == "__main__":
    # 确保数据目录存在
    os.makedirs(DATA_CONFIG["crawl_data_path"], exist_ok=True)

    print("🚀 启动Crawl4AI爬虫客户端...")
    print(
        f"📱 Web界面: http://{CLIENT_SERVER_CONFIG['host']}:{CLIENT_SERVER_CONFIG['port']}"
    )
    print(
        f"🗄️  数据库: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
    )
    print("⏹️  按 Ctrl+C 停止服务")

    app.run(
        host=CLIENT_SERVER_CONFIG["host"],
        port=CLIENT_SERVER_CONFIG["port"],
        debug=CLIENT_SERVER_CONFIG["debug"],
    )
