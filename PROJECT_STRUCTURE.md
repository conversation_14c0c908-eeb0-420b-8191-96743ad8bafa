# Crawl4AI 项目结构（简化版）

## 🎯 架构概述

经过彻底简化，项目现在采用**单服务架构**，将所有功能集成到爬虫客户端中，直接连接数据库进行同步。

```
爬虫客户端 (5000端口) ←→ 直接连接 ←→ MySQL数据库
```

## 📁 项目文件结构

```
crawl4AI/
├── 📱 crawler-client/           # 核心爬虫服务（唯一服务）
│   ├── client.py               # 主服务文件（Flask应用）
│   ├── config.py               # 配置文件（包含数据库配置）
│   ├── deep_crawler.py         # 爬虫核心逻辑
│   ├── database_sync.py        # 数据库同步核心模块
│   ├── local_database_sync.py  # 本地数据库同步服务
│   ├── requirements.txt        # Python依赖
│   ├── templates/              # 前端模板
│   └── crawl_data/             # 数据存储目录
│       ├── details_data.json   # 爬取的数据
│       ├── sync_status.json    # 同步状态
│       └── sync_history.json   # 同步历史
├── 🐳 Docker相关
│   ├── Dockerfile              # Docker镜像构建文件
│   ├── docker-compose.yml      # Docker编排文件
│   └── env.example             # 环境变量示例
├── 🛠️ 脚本工具
│   └── scripts/
│       ├── start.sh            # 启动脚本
│       ├── stop.sh             # 停止脚本
│       └── logs.sh             # 日志查看脚本
├── 📋 构建脚本
│   ├── build.sh                # 构建脚本
│   ├── build_amd64.sh          # AMD64构建脚本
│   └── export_amd64_image.sh   # 镜像导出脚本
└── 📖 文档
    ├── README.md               # 项目说明
    ├── CLAUDE.md               # Claude使用说明
    └── PROJECT_STRUCTURE.md    # 本文档
```

## 🔧 核心文件说明

### 主要服务文件

#### `crawler-client/client.py`
- **作用**：主服务文件，Flask Web应用
- **功能**：
  - 提供Web界面和API接口
  - 集成爬虫功能
  - 集成数据库同步功能
  - 管理本地数据和状态

#### `crawler-client/config.py`
- **作用**：统一配置文件
- **包含**：
  - 爬虫配置
  - 数据库配置
  - 海报下载配置
  - Web服务配置

#### `crawler-client/deep_crawler.py`
- **作用**：爬虫核心逻辑
- **功能**：
  - 使用Playwright进行网页爬取
  - 数据解析和处理
  - 支持动态加载页面

#### `crawler-client/database_sync.py`
- **作用**：数据库同步核心模块
- **功能**：
  - MySQL数据库连接和操作
  - 批量数据同步
  - 海报下载和处理

#### `crawler-client/local_database_sync.py`
- **作用**：本地数据库同步服务
- **功能**：
  - 封装数据库同步逻辑
  - 管理同步历史和统计
  - 提供统一的同步接口

### 配置和部署文件

#### `Dockerfile`
- **作用**：Docker镜像构建配置
- **特点**：
  - 基于Ubuntu 22.04
  - 集成Playwright和Chrome
  - 优化的系统依赖

#### `docker-compose.yml`
- **作用**：Docker服务编排
- **配置**：
  - 单一服务配置
  - 端口映射（5000）
  - 数据卷挂载
  - 环境变量设置

## 🚀 启动方式

### 本地开发
```bash
cd crawler-client
python client.py
```

### Docker部署
```bash
# 构建并启动
./scripts/start.sh

# 或者手动启动
docker-compose up -d
```

## 📊 端口说明

- **5000**：爬虫服务端口（Web界面 + API）

## 🗄️ 数据存储

### 本地文件
- `crawl_data/details_data.json` - 爬取的详细数据
- `crawl_data/sync_status.json` - 本地同步状态
- `crawl_data/sync_history.json` - 同步操作历史

### 数据库
- MySQL数据库存储最终的结构化数据
- 支持海报文件的本地存储和数据库路径记录

## 🔄 主要API接口

### 爬虫功能
- `POST /crawler/start` - 启动爬虫
- `GET /crawler/status` - 获取爬虫状态
- `POST /crawler/stop` - 停止爬虫

### 数据管理
- `GET /crawler/sync/local_data` - 获取本地数据
- `GET /crawler/sync/sync_status` - 获取同步状态
- `POST /crawler/sync/sync_data` - 执行数据同步

### 统计信息
- `GET /crawler/sync/local_statistics` - 获取同步统计
- `GET /crawler/sync/local_history` - 获取同步历史

## 🎉 简化优势

1. **架构简单**：单一服务，易于部署和维护
2. **性能更好**：无网络开销，直接数据库连接
3. **可靠性高**：减少故障点，原子操作
4. **资源节省**：只需一个进程，减少资源占用
5. **开发效率**：统一代码库，便于调试和扩展

## 🔮 扩展建议

如果将来需要扩展，可以考虑：
1. **分布式爬虫**：多实例并行爬取
2. **消息队列**：异步任务处理
3. **缓存层**：Redis缓存热点数据
4. **监控告警**：集成监控和告警系统

但在当前场景下，单服务架构是最优解。
