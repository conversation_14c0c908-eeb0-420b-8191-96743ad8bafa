#!/usr/bin/env python3
"""
测试本地数据库同步功能
验证新的单服务架构是否正常工作
"""

import json
import requests
import time
from typing import Dict, List

# 测试配置
CLIENT_BASE_URL = "http://localhost:5000"

def test_local_sync():
    """测试本地数据库同步接口"""
    print("🧪 测试本地数据库同步接口...")
    
    # 模拟测试数据
    test_data = {
        "selected_ids": ["test-local-1", "test-local-2"],
        "title_updates": {
            "test-local-1": "本地同步测试标题1"
        },
        "short_name_updates": {
            "test-local-2": "本地同步测试短剧名2"
        }
    }
    
    try:
        response = requests.post(
            f"{CLIENT_BASE_URL}/crawler/sync/sync_data",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 本地数据库同步测试成功")
                return True
            else:
                print(f"❌ 本地数据库同步失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_local_statistics():
    """测试本地统计接口"""
    print("🧪 测试本地统计接口...")
    
    try:
        response = requests.get(
            f"{CLIENT_BASE_URL}/crawler/sync/local_statistics",
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 本地统计接口测试成功")
                return True
            else:
                print(f"❌ 本地统计接口失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_local_history():
    """测试本地历史接口"""
    print("🧪 测试本地历史接口...")
    
    try:
        response = requests.get(
            f"{CLIENT_BASE_URL}/crawler/sync/local_history",
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 本地历史接口测试成功")
                return True
            else:
                print(f"❌ 本地历史接口失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_local_data():
    """测试本地数据接口"""
    print("🧪 测试本地数据接口...")
    
    try:
        response = requests.get(
            f"{CLIENT_BASE_URL}/crawler/sync/local_data",
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"数据条数: {result.get('total', 0)}")
        
        if response.status_code == 200:
            if result.get("success"):
                print("✅ 本地数据接口测试成功")
                return True
            else:
                print(f"❌ 本地数据接口失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_service_running():
    """检查服务是否运行"""
    print("🔍 检查爬虫客户端服务状态...")
    
    try:
        response = requests.get(f"{CLIENT_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 爬虫客户端服务运行正常")
            return True
        else:
            print("❌ 爬虫客户端服务异常")
            return False
    except:
        print("❌ 爬虫客户端服务未运行")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试本地数据库同步架构...")
    print("=" * 60)
    
    # 检查服务状态
    if not check_service_running():
        print("⚠️  爬虫客户端服务未运行，请先启动服务")
        print("   启动命令: cd crawler-client && python client.py")
        return
    
    print("\n" + "=" * 60)
    
    # 测试各个接口
    tests = [
        ("本地数据接口", test_local_data),
        ("本地统计接口", test_local_statistics),
        ("本地历史接口", test_local_history),
        ("本地数据库同步", test_local_sync),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        results[test_name] = test_func()
        time.sleep(1)  # 避免请求过快
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 本地数据库同步架构测试完成！")
        print("💡 架构优势:")
        print("   - 单一服务，部署简单")
        print("   - 无网络开销，性能更好")
        print("   - 直接数据库连接，更可靠")
        print("   - 统一代码库，易于维护")
    else:
        print("\n❌ 部分测试失败，请检查服务配置和数据库连接")

if __name__ == "__main__":
    main()
