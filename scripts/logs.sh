#!/bin/bash

# 查看日志脚本
SERVICE=${1:-""}

if [ -z "$SERVICE" ]; then
    echo "查看所有服务日志..."
    if command -v docker-compose &> /dev/null; then
        docker-compose logs -f
    else
        docker compose logs -f
    fi
else
    echo "查看 $SERVICE 服务日志..."
    if command -v docker-compose &> /dev/null; then
        docker-compose logs -f "$SERVICE"
    else
        docker compose logs -f "$SERVICE"
    fi
fi
