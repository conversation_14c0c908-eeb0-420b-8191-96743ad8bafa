#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多服务启动脚本
同时启动爬虫客户端和同步服务器
"""

import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def start_xvfb():
    """启动虚拟显示服务器"""
    try:
        print("🖥️ 启动虚拟显示服务器 (Xvfb)...")
        
        # 检查是否在 Docker 环境中
        if os.path.exists('/.dockerenv'):
            # 启动 Xvfb
            xvfb_process = subprocess.Popen([
                'Xvfb', ':99', '-screen', '0', '1200x800x24', '-ac'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # 等待 Xvfb 启动
            time.sleep(3)
            
            # 检查 Xvfb 是否成功启动
            if xvfb_process.poll() is None:
                print("✅ 虚拟显示服务器启动成功")
                return xvfb_process
            else:
                print("❌ 虚拟显示服务器启动失败")
                return None
        else:
            print("ℹ️ 非 Docker 环境，跳过虚拟显示服务器")
            return None
            
    except Exception as e:
        print(f"❌ 启动虚拟显示服务器失败: {e}")
        return None

def start_crawler_client():
    """启动爬虫客户端"""
    try:
        print("🚀 启动爬虫客户端 (端口 5000)...")
        client_path = project_root / "crawler-client" / "client.py"
        process = subprocess.Popen([
            sys.executable, str(client_path)
        ], cwd=str(project_root))
        return process
    except Exception as e:
        print(f"❌ 启动爬虫客户端失败: {e}")
        return None

def start_sync_server():
    """启动同步服务器"""
    try:
        print("🚀 启动同步服务器 (端口 5001)...")
        server_path = project_root / "sync-server" / "server.py"
        process = subprocess.Popen([
            sys.executable, str(server_path)
        ], cwd=str(project_root))
        return process
    except Exception as e:
        print(f"❌ 启动同步服务器失败: {e}")
        return None

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n🛑 收到信号 {signum}，正在停止服务...")
    sys.exit(0)

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🚀 启动 Crawl4AI 多服务...")
    
    # 启动虚拟显示服务器
    xvfb_process = start_xvfb()
    
    # 启动爬虫客户端
    client_process = start_crawler_client()
    if not client_process:
        print("❌ 爬虫客户端启动失败，退出")
        if xvfb_process:
            xvfb_process.terminate()
        sys.exit(1)
    
    # 等待一下再启动同步服务器
    time.sleep(2)
    
    # 启动同步服务器
    sync_process = start_sync_server()
    if not sync_process:
        print("❌ 同步服务器启动失败，退出")
        client_process.terminate()
        if xvfb_process:
            xvfb_process.terminate()
        sys.exit(1)
    
    print("✅ 所有服务启动成功！")
    print("📋 服务状态:")
    print("   - 爬虫客户端: http://localhost:5000")
    print("   - 同步服务器: http://localhost:5001")
    print("   - 健康检查: http://localhost:5000/health, http://localhost:5001/health")
    if xvfb_process:
        print("   - 虚拟显示: Xvfb :99")
    
    try:
        # 等待进程结束
        while True:
            # 检查进程是否还在运行
            if client_process.poll() is not None:
                print("❌ 爬虫客户端进程已退出")
                break
            if sync_process.poll() is not None:
                print("❌ 同步服务器进程已退出")
                break
            
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在停止服务...")
    
    finally:
        # 清理进程
        print("🧹 清理进程...")
        if client_process:
            client_process.terminate()
            try:
                client_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                client_process.kill()
        
        if sync_process:
            sync_process.terminate()
            try:
                sync_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                sync_process.kill()
        
        if xvfb_process:
            xvfb_process.terminate()
            try:
                xvfb_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                xvfb_process.kill()
        
        print("✅ 所有服务已停止")

if __name__ == "__main__":
    main() 