#!/bin/bash

echo "🚀 启动 crawl4AI 服务..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查镜像是否存在
if ! docker images | grep -q "crawl4ai-app"; then
    echo "🔨 镜像不存在，正在构建..."
    ./build.sh
fi

# 启动服务
echo "🔄 启动容器..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ 爬虫服务启动成功！"
else
    echo "⚠️ 爬虫服务可能还在启动中..."
fi

echo "📍 服务地址："
echo "   - 爬虫服务: http://localhost:5000"
echo "   - 健康检查: http://localhost:5000/health"
echo ""
echo "📋 查看日志: docker-compose logs -f"
 