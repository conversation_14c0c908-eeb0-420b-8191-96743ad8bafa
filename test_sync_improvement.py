#!/usr/bin/env python3
"""
测试同步功能改进的脚本
验证一步式同步是否正常工作
"""

import json
import requests
import time
from typing import Dict, List

# 测试配置
CLIENT_BASE_URL = "http://localhost:5000"
SERVER_BASE_URL = "http://localhost:5001"
API_KEY = "your-api-key-here"  # 需要根据实际配置修改

def test_client_sync_data_direct():
    """测试客户端的一步式同步接口"""
    print("🧪 测试客户端一步式同步接口...")
    
    # 模拟测试数据
    test_data = {
        "selected_ids": ["test-id-1", "test-id-2"],
        "title_updates": {
            "test-id-1": "更新后的标题1"
        },
        "short_name_updates": {
            "test-id-2": "更新后的短剧名2"
        }
    }
    
    try:
        response = requests.post(
            f"{CLIENT_BASE_URL}/crawler/sync/sync_data",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 客户端一步式同步测试成功")
                return True
            else:
                print(f"❌ 客户端一步式同步失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_server_sync_direct():
    """测试服务端的一步式同步接口"""
    print("🧪 测试服务端一步式同步接口...")
    
    # 模拟测试数据
    test_data = {
        "data": [
            {
                "id": "test-direct-1",
                "title": "测试直接同步1",
                "short_name": "测试1",
                "url": "http://example.com/1",
                "poster_url": "http://example.com/poster1.jpg"
            },
            {
                "id": "test-direct-2", 
                "title": "测试直接同步2",
                "short_name": "测试2",
                "url": "http://example.com/2",
                "poster_url": "http://example.com/poster2.jpg"
            }
        ]
    }
    
    try:
        response = requests.post(
            f"{SERVER_BASE_URL}/crawlerapi/v1/sync/direct",
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "X-API-Key": API_KEY
            },
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 服务端一步式同步测试成功")
                return True
            else:
                print(f"❌ 服务端一步式同步失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_services_running():
    """检查服务是否运行"""
    print("🔍 检查服务状态...")
    
    # 检查客户端服务
    try:
        response = requests.get(f"{CLIENT_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 客户端服务运行正常")
            client_running = True
        else:
            print("❌ 客户端服务异常")
            client_running = False
    except:
        print("❌ 客户端服务未运行")
        client_running = False
    
    # 检查服务端服务
    try:
        response = requests.get(f"{SERVER_BASE_URL}/crawlerapi/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务端服务运行正常")
            server_running = True
        else:
            print("❌ 服务端服务异常")
            server_running = False
    except:
        print("❌ 服务端服务未运行")
        server_running = False
    
    return client_running, server_running

def main():
    """主测试函数"""
    print("🚀 开始测试同步功能改进...")
    print("=" * 50)
    
    # 检查服务状态
    client_running, server_running = check_services_running()
    
    if not client_running:
        print("⚠️  客户端服务未运行，请先启动客户端服务")
        print("   启动命令: cd crawler-client && python client.py")
        return
    
    if not server_running:
        print("⚠️  服务端服务未运行，请先启动服务端服务")
        print("   启动命令: cd sync-server && python server.py")
        return
    
    print("\n" + "=" * 50)
    
    # 测试服务端一步式同步接口
    server_test_passed = test_server_sync_direct()
    
    print("\n" + "=" * 50)
    
    # 测试客户端一步式同步接口（需要有实际数据）
    print("⚠️  客户端测试需要有实际的爬取数据，如果没有数据会失败")
    client_test_passed = test_client_sync_data_direct()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   服务端一步式同步: {'✅ 通过' if server_test_passed else '❌ 失败'}")
    print(f"   客户端一步式同步: {'✅ 通过' if client_test_passed else '❌ 失败'}")
    
    if server_test_passed:
        print("\n🎉 同步功能改进测试完成！")
        print("💡 主要改进:")
        print("   - 将原来的两步操作（上传数据 + 执行同步）合并为一步")
        print("   - 减少了网络请求次数，提高了同步效率")
        print("   - 简化了客户端逻辑，降低了出错概率")
    else:
        print("\n❌ 测试失败，请检查服务配置和数据库连接")

if __name__ == "__main__":
    main()
