# Crawl4AI - 分布式爬虫和同步系统

## 项目概述

Crawl4AI是一个分布式的数据爬取和同步系统，专门用于大麦网演出数据的采集、处理和数据库同步。系统采用客户端-服务端架构，支持独立部署和扩展。

## 🏗️ 系统架构

```
┌─────────────────────┐    HTTP API    ┌─────────────────────┐
│                     │◄──────────────►│                     │
│   爬虫客户端         │                │   同步服务端         │
│   (crawler-client)  │                │   (sync-server)     │
│                     │                │                     │
│  ┌─────────────────┐│                │┌─────────────────┐  │
│  │ Web界面         ││                ││ RESTful API     │  │
│  │ 爬虫引擎        ││                ││ 数据库同步      │  │
│  │ 数据管理        ││                ││ 海报下载        │  │
│  │ 服务端通信      ││                ││ 状态管理        │  │
│  └─────────────────┘│                │└─────────────────┘  │
└─────────────────────┘                └─────────────────────┘
                                                 │
                                                 ▼
                                        ┌─────────────────────┐
                                        │    MySQL数据库      │
                                        │                     │
                                        │ • t_repertoire      │
                                        │ • t_theater         │
                                        │ • t_area           │
                                        │ • t_repertoire_*    │
                                        └─────────────────────┘
```

## 📦 项目结构

```
crawl4AI/
├── sync-server/              # 同步服务端
│   ├── server.py            # 服务端主程序
│   ├── sync_service.py      # 同步服务逻辑
│   ├── database_sync.py     # 数据库同步模块
│   ├── config.py           # 服务端配置
│   ├── requirements.txt    # 服务端依赖
│   └── README.md           # 服务端文档
│
├── crawler-client/          # 爬虫客户端
│   ├── client.py           # 客户端主程序
│   ├── deep_crawler.py     # 爬虫引擎
│   ├── sync_client.py      # 服务端API客户端
│   ├── config.py          # 客户端配置
│   ├── templates/         # Web模板
│   ├── requirements.txt   # 客户端依赖
│   └── README.md          # 客户端文档
│
├── README.md              # 项目总体说明
└── 部署说明.md            # 部署指南
```

## 🚀 快速开始

### 方式一：Docker部署（推荐）

```bash
# 构建Docker镜像
./build.sh

# 运行容器
docker run -d \
  --name crawl4ai \
  -p 5000:5000 \
  -p 5001:5001 \
  -v $(pwd)/data:/app/data \
  crawl4ai/app:latest

# 查看容器状态
docker logs crawl4ai

# 测试Playwright功能
docker exec crawl4ai python /app/test_playwright.py
```

### 方式二：本地部署

#### 1. 部署同步服务端

```bash
# 进入服务端目录
cd sync-server

# 安装依赖
pip install -r requirements.txt

# 配置数据库（编辑config.py）
vim config.py

# 启动服务端
python server.py
```

服务端将在 `http://0.0.0.0:5001` 启动。

#### 2. 部署爬虫客户端

```bash
# 进入客户端目录
cd crawler-client

# 安装依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium

# 配置服务端地址（编辑config.py）
vim config.py

# 启动客户端
python client.py
```

客户端将在 `http://localhost:5000` 启动。

## 🔧 功能特性

### 同步服务端 (sync-server)
- ✅ **RESTful API**: 完整的数据同步API接口
- ✅ **数据库同步**: 直接连接MySQL数据库
- ✅ **海报下载**: 自动下载海报图片到本地
- ✅ **数据管理**: 数据接收、存储和状态管理
- ✅ **统计分析**: 详细的同步统计和历史记录
- ✅ **API认证**: 基于API Key的安全认证

### 爬虫客户端 (crawler-client)
- ✅ **Playwright浏览器自动化**: 使用Playwright进行可视化爬取
- ✅ **Element UI分页支持**: 专门优化Element UI分页组件点击
- ✅ **智能分页检测**: 多种分页选择器策略，确保翻页成功
- ✅ **数据爬取**: 支持大麦网演出信息爬取
- ✅ **动态加载**: 处理JavaScript动态内容
- ✅ **Web界面**: 友好的操作和管理界面
- ✅ **数据管理**: 本地数据存储和展示
- ✅ **服务端通信**: 与同步服务端无缝对接
- ✅ **批量操作**: 支持批量上传和同步
- ✅ **进度跟踪**: 实时显示爬取进度和状态

## 📋 使用流程

### 标准工作流程

1. **启动服务端**
   ```bash
   cd sync-server && python server.py
   ```

2. **启动客户端**
   ```bash
   cd crawler-client && python client.py
   ```

3. **数据爬取**
   - 访问客户端Web界面 (http://localhost:5000)
   - 配置爬取参数并开始爬取
   - 监控爬取进度和结果

4. **数据上传**
   - 选择要上传的爬取数据
   - 上传到同步服务端
   - 确认数据接收成功

5. **数据同步**
   - 在服务端选择要同步的数据
   - 执行数据库同步操作
   - 查看同步结果和统计

### API调用流程

```python
# 客户端上传数据
from sync_client import SyncServerClient

client = SyncServerClient()
result = client.upload_data(crawled_data)

# 服务端同步数据
result = client.sync_data(selected_ids)
```

## 🔗 API接口

### 服务端API (端口8080)

- `GET /health` - 健康检查
- `GET /api/v1/info` - API信息
- `POST /api/v1/data/upload` - 上传数据
- `GET /api/v1/data` - 获取数据列表
- `POST /api/v1/sync` - 执行同步
- `GET /api/v1/sync/statistics` - 获取统计

### 客户端API (端口5000)

- `POST /start_crawl` - 开始爬取
- `GET /status` - 爬取状态
- `GET /data` - 本地数据
- `POST /sync/upload_data` - 上传数据
- `POST /sync/execute` - 执行同步

## 🎭 Playwright技术说明

### 浏览器自动化特性
- **Chromium浏览器**: 使用Playwright的Chromium浏览器进行爬取
- **无头模式**: 默认使用无头模式，支持有头模式调试
- **Element UI优化**: 专门针对Element UI分页组件进行优化
- **智能分页**: 多种分页选择器策略，确保翻页成功
- **截图功能**: 支持页面截图，便于调试和验证

### 分页策略
```python
# Element UI分页选择器（优先级最高）
'.el-pager li.number:has-text("2"):not(.active)'
'ul.el-pager li.number:has-text("2"):not(.active)'

# 通用分页选择器
'a:has-text("下一页")'
'button:has-text("Next")'
'.pagination .next'
```

### Docker容器特性
- **非root用户**: 使用playwright用户运行，提高安全性
- **中文字体**: 内置中文字体支持
- **系统依赖**: 完整的Playwright系统依赖
- **健康检查**: 自动健康检查机制

## ⚙️ 配置说明

### 服务端配置 (sync-server/config.py)

```python
# 数据库配置
DATABASE_CONFIG = {
    'host': 'your_mysql_host',
    'port': 3306,
    'user': 'your_username',
    'password': 'your_password',
    'database': 'your_database'
}

# 服务器配置
SERVER_CONFIG = {
    'host': '0.0.0.0',
    'port': 8080,
    'debug': False
}
```

### 客户端配置 (crawler-client/config.py)

```python
# 同步服务端配置
SYNC_SERVER_CONFIG = {
    'base_url': 'http://your_server:8080/api/v1',
    'api_key': 'crawl4ai_sync_2024',
    'timeout': 30
}

# 爬虫配置
CRAWLER_CONFIG = {
    'default_max_items': 20,
    'default_delay': 2.0,
    'use_dynamic_loading': True
}
```

## 🐳 Docker部署

### 服务端Docker

```dockerfile
# sync-server/Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8080
CMD ["python", "server.py"]
```

### 客户端Docker

```dockerfile
# crawler-client/Dockerfile
FROM python:3.9-slim
# 安装Chrome和相关依赖
RUN apt-get update && apt-get install -y google-chrome-stable
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "client.py"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  sync-server:
    build: ./sync-server
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./downloads:/app/downloads

  crawler-client:
    build: ./crawler-client
    ports:
      - "5000:5000"
    volumes:
      - ./crawl_data:/app/crawl_data
    depends_on:
      - sync-server
```

## 📊 监控和维护

### 健康检查

```bash
# 检查服务端状态
curl http://localhost:8080/health

# 检查客户端状态
curl http://localhost:5000/status
```

### 日志监控

- 服务端日志：标准输出，包含API请求和同步操作
- 客户端日志：标准输出，包含爬取进度和服务端通信

### 数据备份

```bash
# 备份服务端数据
tar -czf sync-server-backup.tar.gz sync-server/data sync-server/downloads

# 备份客户端数据
tar -czf crawler-client-backup.tar.gz crawler-client/crawl_data
```

## 🔒 安全考虑

### API安全
- 使用API Key认证
- 建议在生产环境中使用HTTPS
- 定期更换API密钥

### 网络安全
- 配置防火墙规则
- 限制服务端访问来源
- 使用VPN或内网部署

### 数据安全
- 定期备份重要数据
- 加密敏感配置信息
- 监控异常访问

## 🚨 故障排除

### 常见问题

1. **服务端启动失败**
   - 检查端口是否被占用
   - 验证数据库连接配置
   - 确认依赖是否完整安装

2. **客户端连接服务端失败**
   - 检查服务端是否启动
   - 验证网络连通性
   - 确认API密钥配置

3. **爬取失败**
   - 检查目标网站可访问性
   - 验证Chrome浏览器安装
   - 调整爬取参数

4. **数据库同步失败**
   - 检查数据库连接
   - 验证表结构是否正确
   - 确认数据格式符合要求

### 性能优化

- 适当调整爬取延迟和并发数
- 定期清理历史数据和日志
- 监控系统资源使用情况
- 使用数据库连接池

## 📈 扩展建议

### 水平扩展
- 部署多个客户端实例
- 使用负载均衡器分发请求
- 数据库读写分离

### 功能扩展
- 支持更多数据源
- 增加数据清洗和验证
- 添加实时监控和告警
- 实现数据可视化

## 📞 支持与联系

如遇到问题，请按以下步骤排查：

1. 查看相关日志输出
2. 检查配置文件设置
3. 验证网络连接状态
4. 确认依赖版本兼容性

## 📄 许可证

本项目遵循相关开源许可证，请合规使用。 