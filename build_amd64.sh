#!/bin/bash

# 设置错误时退出
set -e

echo "🚀 开始构建 crawl4AI amd64 架构 Docker 镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查是否支持 buildx
if ! docker buildx version > /dev/null 2>&1; then
    echo "❌ Docker buildx 不可用，请升级 Docker 或启用 buildx"
    exit 1
fi

# 创建并使用新的构建器（如果不存在）
BUILDER_NAME="crawl4ai-builder"
if ! docker buildx inspect $BUILDER_NAME > /dev/null 2>&1; then
    echo "🔧 创建新的构建器: $BUILDER_NAME"
    docker buildx create --name $BUILDER_NAME --use
else
    echo "🔧 使用现有构建器: $BUILDER_NAME"
    docker buildx use $BUILDER_NAME
fi

# 启动构建器
echo "🚀 启动构建器..."
docker buildx inspect --bootstrap

# 清理旧的镜像和容器
echo "🧹 清理旧的镜像和容器..."
docker-compose down --remove-orphans 2>/dev/null || true
docker rmi crawl4ai-app 2>/dev/null || true

# 构建 amd64 架构镜像
echo "🔨 构建 amd64 架构镜像..."
docker buildx build \
    --platform linux/amd64 \
    --tag crawl4ai-app:latest \
    --tag crawl4ai-app:amd64 \
    --file Dockerfile \
    --load \
    .

# 验证镜像构建
echo "✅ 镜像构建完成"
echo "📋 镜像信息："
docker images | grep crawl4ai

# 显示镜像架构信息
echo "🔍 镜像架构信息："
docker inspect crawl4ai-app:latest | grep -A 5 "Architecture"

echo " 构建完成！"
echo "📝 使用以下命令启动服务："
echo "   docker-compose up -d"
echo ""
echo "📝 查看日志："
echo "   docker-compose logs -f"
echo ""
echo "📝 停止服务："
echo "   docker-compose down"
echo ""
echo " 服务地址："
echo "   - 爬虫客户端: http://localhost:5000"
echo "   - 同步服务器: http://localhost:5001"
echo "   - 健康检查: http://localhost:5000/health, http://localhost:5001/health" 