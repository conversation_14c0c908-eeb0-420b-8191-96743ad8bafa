# 使用本地已有的Python 3.11镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV DISPLAY=:99

# 使用国内镜像源解决网络问题 - 兼容新版本 Debian
RUN echo "deb https://mirrors.aliyun.com/debian/ bookworm main non-free non-free-firmware contrib" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ bookworm-updates main non-free non-free-firmware contrib" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security bookworm-security main non-free non-free-firmware contrib" >> /etc/apt/sources.list

# 安装系统依赖 - 完整的 Playwright 依赖包
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    procps \
    curl \
    # 核心系统库
    libxss1 \
    libnss3 \
    libnspr4 \
    # 图形和显示相关库
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libasound2 \
    libpango-1.0-0 \
    libcairo2 \
    libcairo-gobject2 \
    libcairo2-dev \
    libatspi2.0-0 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    # 字体相关
    fonts-liberation \
    fonts-noto-cjk \
    fonts-noto-cjk-extra \
    # 开发库（确保完整安装）
    libnss3-dev \
    libatk-bridge2.0-dev \
    libdrm-dev \
    libxkbcommon-dev \
    libxcomposite-dev \
    libxdamage-dev \
    libxrandr-dev \
    libgbm-dev \
    libasound2-dev \
    libpango1.0-dev \
    libcairo2-dev \
    libatspi2.0-dev \
    libgtk-3-dev \
    libgdk-pixbuf2.0-dev \
    # 虚拟显示和X11相关
    xvfb \
    x11-utils \
    x11-apps \
    x11-xserver-utils \
    # 额外的图形库（确保兼容性）
    libx11-6 \
    libx11-dev \
    libxext6 \
    libxext-dev \
    libxrender1 \
    libxrender-dev \
    libxfixes3 \
    libxfixes-dev \
    libxi6 \
    libxi-dev \
    libxtst6 \
    libxtst-dev \
    libxrandr2 \
    libxrandr-dev \
    libxss1 \
    libxss-dev \
    # 音频相关
    libpulse0 \
    libpulse-dev \
    # 网络和安全
    ca-certificates \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 使用国内 PyPI 镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 复制所有requirements文件
COPY crawler-client/requirements.txt ./crawler-client/
COPY sync-server/requirements.txt ./sync-server/

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r crawler-client/requirements.txt && \
    pip install --no-cache-dir -r sync-server/requirements.txt

# 安装Playwright浏览器和依赖
RUN playwright install chromium && \
    playwright install-deps chromium

# 验证关键系统库是否存在
RUN echo "🔍 验证关键系统库..." && \
    echo "检查 libnss3:" && ls -la /usr/lib/x86_64-linux-gnu/libnss3.so* && \
    echo "检查 libatk:" && ls -la /usr/lib/x86_64-linux-gnu/libatk-1.0.so* && \
    echo "检查 libgtk:" && ls -la /usr/lib/x86_64-linux-gnu/libgtk-3.so* && \
    echo "检查 libgdk:" && ls -la /usr/lib/x86_64-linux-gnu/libgdk-3.so* && \
    echo "检查 libpango:" && ls -la /usr/lib/x86_64-linux-gnu/libpango-1.0.so* && \
    echo "检查 libcairo:" && ls -la /usr/lib/x86_64-linux-gnu/libcairo.so* && \
    echo "检查 libgdk_pixbuf:" && ls -la /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so* && \
    echo "检查 libatspi:" && ls -la /usr/lib/x86_64-linux-gnu/libatspi.so* && \
    echo "检查 libdrm:" && ls -la /usr/lib/x86_64-linux-gnu/libdrm.so* && \
    echo "检查 libxkbcommon:" && ls -la /usr/lib/x86_64-linux-gnu/libxkbcommon.so* && \
    echo "检查 libxcomposite:" && ls -la /usr/lib/x86_64-linux-gnu/libXcomposite.so* && \
    echo "检查 libxdamage:" && ls -la /usr/lib/x86_64-linux-gnu/libXdamage.so* && \
    echo "检查 libxrandr:" && ls -la /usr/lib/x86_64-linux-gnu/libXrandr.so* && \
    echo "检查 libgbm:" && ls -la /usr/lib/x86_64-linux-gnu/libgbm.so* && \
    echo "检查 libasound:" && ls -la /usr/lib/x86_64-linux-gnu/libasound.so* && \
    echo "✅ 所有关键系统库验证通过"

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/crawler-client/crawl_data && \
    mkdir -p /app/sync-server/downloads/posters && \
    mkdir -p /app/sync-server/data && \
    mkdir -p /app/logs

# 设置权限
RUN chmod +x /app/scripts/*.sh

# 创建 Playwright 优化配置
RUN echo '#!/bin/bash' > /app/scripts/start_xvfb.sh && \
    echo 'Xvfb :99 -screen 0 1200x800x24 > /dev/null 2>&1 &' >> /app/scripts/start_xvfb.sh && \
    echo 'sleep 2' >> /app/scripts/start_xvfb.sh && \
    chmod +x /app/scripts/start_xvfb.sh

# 创建 Playwright 测试脚本
RUN echo '#!/usr/bin/env python3' > /app/test_playwright_setup.py && \
    echo 'import sys' >> /app/test_playwright_setup.py && \
    echo 'from playwright.sync_api import sync_playwright' >> /app/test_playwright_setup.py && \
    echo 'try:' >> /app/test_playwright_setup.py && \
    echo '    p = sync_playwright().start()' >> /app/test_playwright_setup.py && \
    echo '    browser = p.chromium.launch(headless=True, args=["--no-sandbox"])' >> /app/test_playwright_setup.py && \
    echo '    context = browser.new_context()' >> /app/test_playwright_setup.py && \
    echo '    page = context.new_page()' >> /app/test_playwright_setup.py && \
    echo '    page.goto("https://httpbin.org/html", timeout=30000)' >> /app/test_playwright_setup.py && \
    echo '    print("✅ Playwright 设置验证成功")' >> /app/test_playwright_setup.py && \
    echo '    browser.close()' >> /app/test_playwright_setup.py && \
    echo '    p.stop()' >> /app/test_playwright_setup.py && \
    echo '    sys.exit(0)' >> /app/test_playwright_setup.py && \
    echo 'except Exception as e:' >> /app/test_playwright_setup.py && \
    echo '    print(f"❌ Playwright 设置验证失败: {e}")' >> /app/test_playwright_setup.py && \
    echo '    sys.exit(1)' >> /app/test_playwright_setup.py && \
    chmod +x /app/test_playwright_setup.py

# 暴露端口
EXPOSE 5000 5001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/health', timeout=5)" || exit 1

# 启动命令（使用supervisor管理多进程）
CMD ["python", "scripts/start_services.py"]
 