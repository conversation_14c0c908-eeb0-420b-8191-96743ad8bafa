#!/bin/bash

# CentOS 完整的 Docker 解决方案
# 解决 Playwright 超时问题的一站式脚本

set -e

echo "🔧 CentOS Playwright 完整解决方案"
echo "=================================================="

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 停止并删除现有容器
echo "🧹 清理现有容器..."
docker stop crawl4ai-app 2>/dev/null || true
docker rm crawl4ai-app 2>/dev/null || true

# 检查是否需要重新构建镜像
if ! docker images | grep -q "crawl4ai-app"; then
    echo "📦 构建 Docker 镜像（包含完整 Playwright 依赖）..."
    echo "   这可能需要几分钟时间..."
    
    # 删除旧镜像（如果存在）
    docker rmi crawl4ai-app:latest 2>/dev/null || true
    
    # 构建新镜像
    docker buildx build \
        --platform linux/amd64 \
        --tag crawl4ai-app:latest \
        --progress=plain \
        --no-cache \
        .
    
    if [ $? -eq 0 ]; then
        echo "✅ 镜像构建成功！"
    else
        echo "❌ 镜像构建失败"
        exit 1
    fi
    
    # 验证镜像
    echo ""
    echo "🧪 验证 Playwright 功能..."
    docker run --rm \
        --platform linux/amd64 \
        crawl4ai-app:latest \
        python /app/test_playwright_setup.py
    
    if [ $? -eq 0 ]; then
        echo "✅ Playwright 验证通过"
    else
        echo "❌ Playwright 验证失败"
        exit 1
    fi
else
    echo "✅ 镜像已存在，跳过构建"
fi

# CentOS 优化的运行参数
echo ""
echo "🚀 使用 CentOS 优化配置启动容器..."

docker run -d \
    --name crawl4ai-app \
    --platform linux/amd64 \
    --memory=4g \
    --memory-swap=6g \
    --cpus=2.0 \
    --shm-size=2g \
    -p 5000:5000 \
    -p 5001:5001 \
    --privileged \
    --security-opt seccomp=unconfined \
    --cap-add=SYS_ADMIN \
    --cap-add=SYS_PTRACE \
    --cap-add=NET_ADMIN \
    --cap-add=NET_RAW \
    -e DISPLAY=:99 \
    -e PYTHONPATH=/app \
    -e PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    -e DEBIAN_FRONTEND=noninteractive \
    -v /tmp/.X11-unix:/tmp/.X11-unix \
    -v $(pwd)/crawler-client/crawl_data:/app/crawler-client/crawl_data \
    -v $(pwd)/sync-server/data:/app/sync-server/data \
    -v $(pwd)/sync-server/downloads:/app/sync-server/downloads \
    -v $(pwd)/logs:/app/logs \
    crawl4ai-app:latest

echo "✅ 容器启动成功！"
echo ""
echo "📋 容器信息:"
docker ps | grep crawl4ai-app
echo ""
echo "🔗 服务地址:"
echo "   - 爬虫客户端: http://localhost:5000"
echo "   - 同步服务器: http://localhost:5001"
echo ""

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo ""
echo "🏥 检查服务状态..."
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ 爬虫客户端健康检查通过"
else
    echo "❌ 爬虫客户端健康检查失败"
fi

if curl -f http://localhost:5001/health > /dev/null 2>&1; then
    echo "✅ 同步服务器健康检查通过"
else
    echo "❌ 同步服务器健康检查失败"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📝 常用命令:"
echo "   查看日志: docker logs -f crawl4ai-app"
echo "   进入容器: docker exec -it crawl4ai-app bash"
echo "   停止服务: docker stop crawl4ai-app"
echo "   重启服务: docker restart crawl4ai-app"
echo ""
echo "💡 如果仍有问题:"
echo "   1. 检查容器日志: docker logs crawl4ai-app"
echo "   2. 检查系统资源: docker stats crawl4ai-app"
echo "   3. 检查网络连接: docker exec crawl4ai-app ping -c 3 google.com" 