#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Crawl4AI 同步服务端
提供数据同步API接口，支持海报下载和数据库同步功能
"""

import json
import os
import logging
from datetime import datetime
from functools import wraps
from flask import Flask, jsonify, request
from sync_service import SyncService
from config import API_CONFIG, SERVER_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 全局同步服务实例
sync_service = SyncService()

# API认证装饰器
def require_api_key(f):
    """API密钥验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头或请求体中获取API密钥
        api_key = None
        
        # 优先从请求头获取
        if 'X-API-Key' in request.headers:
            api_key = request.headers.get('X-API-Key')
        # 其次从查询参数获取
        elif 'api_key' in request.args:
            api_key = request.args.get('api_key')
        # 最后从请求体获取
        elif request.is_json and request.get_json() and 'api_key' in request.get_json():
            api_key = request.get_json().get('api_key')
        
        if not api_key or api_key != API_CONFIG['api_key']:
            logger.warning(f"无效的API密钥访问尝试: {request.remote_addr}")
            return jsonify({
                'success': False,
                'error': 'Invalid or missing API key',
                'message': '无效或缺失的API密钥'
            }), 401
        
        return f(*args, **kwargs)
    return decorated_function

# ==================== API 路由 ====================

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'success': True,
        'service': 'Crawl4AI Sync Server',
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

@app.route(f'/crawlerapi/{API_CONFIG["version"]}/info', methods=['GET'])
def api_info():
    """API信息接口（无需认证）"""
    return jsonify({
        'success': True,
        'service_name': 'Crawl4AI Sync Server',
        'version': API_CONFIG['version'],
        'description': '大麦网数据同步服务API',
        'endpoints': {
            'GET /health': '健康检查（无需认证）',
            'GET /crawlerapi/v1/info': '获取API信息（无需认证）',
            'POST /crawlerapi/v1/data/upload': '上传爬取数据',
            'GET /crawlerapi/v1/data': '获取数据列表',
            'GET /crawlerapi/v1/sync/status': '获取同步状态',
            'GET /crawlerapi/v1/sync/history': '获取同步历史',
            'GET /crawlerapi/v1/sync/statistics': '获取同步统计',
            'POST /crawlerapi/v1/sync': '执行数据同步'
        },
        'authentication': {
            'type': 'API Key',
            'methods': [
                'Header: X-API-Key',
                'Query Parameter: api_key',
                'Request Body: api_key'
            ]
        }
    })

@app.route(f'/crawlerapi/{API_CONFIG["version"]}/data/upload', methods=['POST'])
@require_api_key
def upload_data():
    """上传爬取数据接口"""
    try:
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type must be application/json',
                'message': '请求必须是JSON格式'
            }), 400
        
        data = request.get_json()
        uploaded_data = data.get('data', [])
        
        if not uploaded_data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'message': '没有提供数据'
            }), 400
        
        # 保存上传的数据
        result = sync_service.save_uploaded_data(uploaded_data)
        
        if result['success']:
            logger.info(f"成功接收数据: {result['saved_count']} 条记录")
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"数据上传失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '数据上传过程发生错误'
        }), 500

@app.route(f'/crawlerapi/{API_CONFIG["version"]}/data', methods=['GET'])
@require_api_key
def api_get_data():
    """API获取数据列表"""
    try:
        # 获取查询参数
        include_sync_status = request.args.get('include_sync_status', 'true').lower() == 'true'
        
        # 使用同步服务获取数据
        data_list = sync_service.get_data_list(include_sync_status=include_sync_status)
        summary = sync_service.get_data_summary()
        
        return jsonify({
            'success': True,
            'data': data_list,
            'summary': summary,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取数据失败'
        }), 500

@app.route(f'/crawlerapi/{API_CONFIG["version"]}/sync/status', methods=['GET'])
@require_api_key
def api_get_sync_status():
    """API获取同步状态"""
    try:
        sync_status = sync_service.load_sync_status()
        summary = sync_service.get_data_summary()
        
        return jsonify({
            'success': True,
            'sync_status': sync_status,
            'summary': summary,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取同步状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取同步状态失败'
        }), 500

@app.route(f'/crawlerapi/{API_CONFIG["version"]}/sync/history', methods=['GET'])
@require_api_key
def api_get_sync_history():
    """API获取同步历史"""
    try:
        # 获取查询参数
        limit = int(request.args.get('limit', 10))
        offset = int(request.args.get('offset', 0))
        
        history = sync_service.load_sync_history()
        
        # 分页处理
        total = len(history)
        start = offset
        end = offset + limit
        paginated_history = history[start:end] if history else []
        
        return jsonify({
            'success': True,
            'history': paginated_history,
            'pagination': {
                'total': total,
                'limit': limit,
                'offset': offset,
                'has_more': end < total
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取同步历史失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取同步历史失败'
        }), 500

@app.route(f'/crawlerapi/{API_CONFIG["version"]}/sync/statistics', methods=['GET'])
@require_api_key
def api_get_sync_statistics():
    """API获取同步统计信息"""
    try:
        statistics = sync_service.get_sync_statistics()
        summary = sync_service.get_data_summary()
        
        return jsonify({
            'success': True,
            'statistics': statistics,
            'data_summary': summary,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取同步统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取同步统计失败'
        }), 500

@app.route(f'/crawlerapi/{API_CONFIG["version"]}/sync', methods=['POST'])
@require_api_key
def api_sync_data():
    """API执行数据同步"""
    try:
        # 获取请求数据
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type must be application/json',
                'message': '请求必须是JSON格式'
            }), 400
        
        data = request.get_json()
        selected_ids = data.get('selected_ids', [])
        
        if not selected_ids:
            return jsonify({
                'success': False,
                'error': 'selected_ids is required',
                'message': '必须提供要同步的记录ID列表'
            }), 400
        
        logger.info(f"API请求同步 {len(selected_ids)} 条记录")
        
        # 执行同步
        result = sync_service.perform_sync(selected_ids)
        
        # 记录同步结果
        if result['success']:
            logger.info(f"API同步成功: {result['message']}")
            return jsonify(result)
        else:
            logger.error(f"API同步失败: {result['message']}")
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"API同步过程发生错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'API同步过程发生错误'
        }), 500

# ==================== 错误处理 ====================

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found',
        'message': 'API端点不存在'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    """405错误处理"""
    return jsonify({
        'success': False,
        'error': 'Method not allowed',
        'message': '请求方法不被允许'
    }), 405

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"服务器内部错误: {error}")
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': '服务器内部错误'
    }), 500

if __name__ == '__main__':
    logger.info("🚀 启动Crawl4AI同步服务端...")
    logger.info(f"📡 API端点: http://{SERVER_CONFIG['host']}:{SERVER_CONFIG['port']}/crawlerapi/{API_CONFIG['version']}")
    logger.info(f"🔑 API密钥: {API_CONFIG['api_key']}")
    logger.info("⏹️  按 Ctrl+C 停止服务")
    
    app.run(
        host=SERVER_CONFIG['host'],
        port=SERVER_CONFIG['port'],
        debug=SERVER_CONFIG['debug']
    ) 