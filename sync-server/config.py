#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步服务端配置文件
支持环境变量配置
"""

import os

# 数据库配置 - 支持环境变量
DATABASE_CONFIG = {
    'host': os.environ.get('DB_HOST', '*************'),
    'port': int(os.environ.get('DB_PORT', 3306)),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', 'VYw^%jk6U^'),
    'database': os.environ.get('DB_DATABASE', 'digital_collection')
}

# 文件路径配置
POSTER_DOWNLOAD_PATH = os.path.join(os.path.dirname(__file__), 'downloads')  # 海报下载路径
#POSTER_DOWNLOAD_PATH='/home/<USER>/digital-collections/upload'
#POSTER_DOWNLOAD_PATH='/Users/<USER>/Documents/git/crawl4AI/sync-server/downloads/posters'

DATA_STORAGE_PATH = os.path.join(os.path.dirname(__file__), 'data')  # 数据存储路径

# 下载配置
DOWNLOAD_CONFIG = {
    'timeout': 30,  # 下载超时时间（秒）
    'retry_times': 3,  # 重试次数
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
}

# API配置
API_CONFIG = {
    'api_key': os.environ.get('API_KEY', 'crawl4ai_sync_2024'),  # API密钥，支持环境变量
    'rate_limit': {
        'requests_per_minute': 60,  # 每分钟最大请求数
        'requests_per_hour': 1000   # 每小时最大请求数
    },
    'version': 'v1'  # API版本
}

# 服务器配置 - 支持环境变量
SERVER_CONFIG = {
    'host': os.environ.get('SYNC_HOST', '0.0.0.0'),  # 监听地址
    'port': int(os.environ.get('SYNC_PORT', 5001)),   # 监听端口
    'debug': os.environ.get('SYNC_DEBUG', 'false').lower() == 'true'  # 生产环境建议关闭debug
}

# 确保目录存在
os.makedirs(POSTER_DOWNLOAD_PATH, exist_ok=True)
os.makedirs(DATA_STORAGE_PATH, exist_ok=True)

# 打印配置信息（仅调试时）
if SERVER_CONFIG['debug']:
    print(f"🔧 数据库配置: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}")
    print(f"🔧 同步服务器配置: {SERVER_CONFIG['host']}:{SERVER_CONFIG['port']}") 