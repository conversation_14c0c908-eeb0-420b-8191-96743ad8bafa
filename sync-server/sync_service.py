"""
同步服务模块 - 简化版
专注于数据库同步功能，不维护本地文件状态
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List

from database_sync import create_database_sync

# 配置日志
logger = logging.getLogger(__name__)


class SyncService:
    """同步服务类，专注于数据库同步功能"""

    def __init__(self):
        # 只保留同步历史文件，用于记录同步操作日志
        self.sync_history_file = "data/sync_history.json"

    def load_sync_history(self) -> List[Dict]:
        """加载同步历史"""
        try:
            if os.path.exists(self.sync_history_file):
                with open(self.sync_history_file, "r", encoding="utf-8") as f:
                    history = json.load(f)
                    return history
            else:
                return []
        except Exception as e:
            logger.error(f"加载同步历史失败: {e}")
            return []

    def save_sync_history(self, history_record: Dict) -> bool:
        """保存同步历史记录"""
        try:
            history = self.load_sync_history()
            history.append(history_record)

            # 只保留最近100条记录
            if len(history) > 100:
                history = history[-100:]

            os.makedirs(os.path.dirname(self.sync_history_file), exist_ok=True)
            with open(self.sync_history_file, "w", encoding="utf-8") as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存同步历史失败: {e}")
            return False

    def get_sync_statistics(self) -> Dict:
        """获取同步统计信息"""
        history = self.load_sync_history()

        if not history:
            return {
                "total_syncs": 0,
                "success_rate": 0,
                "last_sync": None,
                "total_records_synced": 0,
            }

        total_syncs = len(history)
        successful_syncs = sum(1 for record in history if record.get("success", False))
        success_rate = (successful_syncs / total_syncs * 100) if total_syncs > 0 else 0

        # 计算总同步记录数
        total_records_synced = 0
        for record in history:
            result = record.get("result", {})
            total_records_synced += result.get("success", 0)

        # 获取最后一次同步时间
        last_sync = history[-1]["timestamp"] if history else None

        return {
            "total_syncs": total_syncs,
            "success_rate": round(success_rate, 2),
            "last_sync": last_sync,
            "total_records_synced": total_records_synced,
        }

    def perform_direct_sync(self, data_list: List[Dict]) -> Dict:
        """一步式数据同步：直接接收数据并同步到数据库"""
        start_time = datetime.now()

        if not data_list:
            return {
                "success": False,
                "message": "没有提供要同步的数据",
                "timestamp": start_time.isoformat(),
            }

        logger.info(f"开始一步式同步 {len(data_list)} 条记录到MySQL数据库")

        try:
            # 创建数据库同步实例
            db_sync = create_database_sync()

            # 连接数据库
            if not db_sync.connect():
                return {
                    "success": False,
                    "message": "数据库连接失败",
                    "timestamp": start_time.isoformat(),
                }

            try:
                # 批量同步数据到数据库
                result = db_sync.sync_multiple_items(data_list)

                # 保存同步历史
                end_time = datetime.now()
                selected_ids = [item["id"] for item in data_list]
                history_record = {
                    "timestamp": start_time.isoformat(),
                    "duration": (end_time - start_time).total_seconds(),
                    "selected_ids": selected_ids,
                    "result": result,
                    "success": result["failed"] == 0,
                    "sync_type": "direct",  # 标记为一步式同步
                }
                self.save_sync_history(history_record)

                result_message = f"一步式数据库同步完成：成功 {result['success']} 条，跳过 {result['skipped']} 条，失败 {result['failed']} 条"
                logger.info(result_message)

                return {
                    "success": True,
                    "message": result_message,
                    "result": result,
                    "timestamp": start_time.isoformat(),
                    "duration": (end_time - start_time).total_seconds(),
                }

            finally:
                db_sync.disconnect()

        except Exception as e:
            error_message = f"一步式数据库同步过程发生错误: {str(e)}"
            logger.error(error_message)
            return {
                "success": False,
                "message": error_message,
                "timestamp": start_time.isoformat(),
            }


# 创建全局同步服务实例
sync_service = SyncService()
