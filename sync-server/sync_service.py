#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步服务模块
提供数据同步的核心业务逻辑，支持Web界面和API调用
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from database_sync import create_database_sync

# 配置日志
logger = logging.getLogger(__name__)


class SyncService:
    """同步服务类，封装所有同步相关的业务逻辑"""

    def __init__(self):
        self.crawl_data_file = "data/details_data.json"
        self.sync_status_file = "data/sync_status.json"
        self.sync_history_file = "data/sync_history.json"

    def load_crawled_data(self) -> List[Dict]:
        """加载爬取的数据"""
        try:
            if os.path.exists(self.crawl_data_file):
                with open(self.crawl_data_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    logger.info(f"加载了 {len(data)} 条爬取数据")
                    return data
            else:
                logger.warning(f"数据文件不存在: {self.crawl_data_file}")
                return []
        except Exception as e:
            logger.error(f"加载爬取数据失败: {e}")
            return []

    def load_sync_status(self) -> Dict[str, bool]:
        """加载同步状态"""
        try:
            if os.path.exists(self.sync_status_file):
                with open(self.sync_status_file, "r", encoding="utf-8") as f:
                    status = json.load(f)
                    logger.info(f"加载了 {len(status)} 条同步状态")
                    return status
            else:
                logger.info("同步状态文件不存在，返回空状态")
                return {}
        except Exception as e:
            logger.error(f"加载同步状态失败: {e}")
            return {}

    def save_sync_status(self, sync_status: Dict[str, bool]) -> bool:
        """保存同步状态"""
        try:
            os.makedirs(os.path.dirname(self.sync_status_file), exist_ok=True)
            with open(self.sync_status_file, "w", encoding="utf-8") as f:
                json.dump(sync_status, f, ensure_ascii=False, indent=2)
            logger.info(f"保存了 {len(sync_status)} 条同步状态")
            return True
        except Exception as e:
            logger.error(f"保存同步状态失败: {e}")
            return False

    def load_sync_history(self) -> List[Dict]:
        """加载同步历史"""
        try:
            if os.path.exists(self.sync_history_file):
                with open(self.sync_history_file, "r", encoding="utf-8") as f:
                    history = json.load(f)
                    return history
            else:
                return []
        except Exception as e:
            logger.error(f"加载同步历史失败: {e}")
            return []

    def save_sync_history(self, history_record: Dict) -> bool:
        """保存同步历史记录"""
        try:
            history = self.load_sync_history()
            history.append(history_record)

            # 只保留最近100条记录
            if len(history) > 100:
                history = history[-100:]

            os.makedirs(os.path.dirname(self.sync_history_file), exist_ok=True)
            with open(self.sync_history_file, "w", encoding="utf-8") as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存同步历史失败: {e}")
            return False

    def get_data_summary(self) -> Dict:
        """获取数据概览"""
        all_data = self.load_crawled_data()
        sync_status = self.load_sync_status()

        total = len(all_data)
        synced = sum(1 for item in all_data if sync_status.get(item["id"], False))
        unsynced = total - synced

        return {
            "total": total,
            "synced": synced,
            "unsynced": unsynced,
            "sync_rate": round(synced / total * 100, 2) if total > 0 else 0,
        }

    def get_data_list(self, include_sync_status: bool = True) -> List[Dict]:
        """获取数据列表，可选择是否包含同步状态"""
        all_data = self.load_crawled_data()

        if include_sync_status:
            sync_status = self.load_sync_status()
            for item in all_data:
                item["is_synced"] = sync_status.get(item["id"], False)

        return all_data

    def validate_sync_request(
        self, selected_ids: List[str]
    ) -> Tuple[bool, str, List[Dict]]:
        """验证同步请求"""
        if not selected_ids:
            return False, "请选择要同步的记录", []

        if not isinstance(selected_ids, list):
            return False, "selected_ids 必须是数组格式", []

        # 加载数据
        all_data = self.load_crawled_data()
        if not all_data:
            return False, "没有可同步的数据", []

        # 准备要同步的数据
        sync_data_list = []
        for item_id in selected_ids:
            # 查找对应的数据
            item_data = None
            for item in all_data:
                if item["id"] == item_id:
                    item_data = item
                    break

            if item_data:
                sync_data_list.append(item_data)
            else:
                logger.warning(f"未找到ID为 {item_id} 的记录")

        if not sync_data_list:
            return False, "没有找到要同步的有效记录", []

        return True, "", sync_data_list

    def perform_sync(self, selected_ids: List[str]) -> Dict:
        """执行同步操作"""
        start_time = datetime.now()

        # 验证请求
        is_valid, error_msg, sync_data_list = self.validate_sync_request(selected_ids)
        if not is_valid:
            return {
                "success": False,
                "message": error_msg,
                "timestamp": start_time.isoformat(),
            }

        logger.info(f"开始同步 {len(sync_data_list)} 条记录到MySQL数据库")

        try:
            # 创建数据库同步实例
            db_sync = create_database_sync()

            # 连接数据库
            if not db_sync.connect():
                return {
                    "success": False,
                    "message": "数据库连接失败",
                    "timestamp": start_time.isoformat(),
                }

            try:
                # 批量同步数据
                result = db_sync.sync_multiple_items(sync_data_list)

                # 更新同步状态
                sync_status = self.load_sync_status()

                # 只标记真正成功的记录
                for item_id in result.get("success_ids", []):
                    sync_status[item_id] = True

                # 跳过的记录也标记为已同步（因为数据已存在）
                for item_id in result.get("skipped_ids", []):
                    sync_status[item_id] = True

                self.save_sync_status(sync_status)

                # 保存同步历史
                end_time = datetime.now()
                history_record = {
                    "timestamp": start_time.isoformat(),
                    "duration": (end_time - start_time).total_seconds(),
                    "selected_ids": selected_ids,
                    "result": result,
                    "success": result["failed"] == 0,
                }
                self.save_sync_history(history_record)

                result_message = f"数据库同步完成：成功 {result['success']} 条，跳过 {result['skipped']} 条，失败 {result['failed']} 条"
                logger.info(result_message)

                return {
                    "success": True,
                    "message": result_message,
                    "result": result,
                    "timestamp": start_time.isoformat(),
                    "duration": (end_time - start_time).total_seconds(),
                }

            finally:
                db_sync.disconnect()

        except Exception as e:
            error_message = f"数据库同步过程发生错误: {str(e)}"
            logger.error(error_message)
            return {
                "success": False,
                "message": error_message,
                "timestamp": start_time.isoformat(),
            }

    def get_sync_statistics(self) -> Dict:
        """获取同步统计信息"""
        history = self.load_sync_history()

        if not history:
            return {
                "total_syncs": 0,
                "success_rate": 0,
                "last_sync": None,
                "total_records_synced": 0,
            }

        total_syncs = len(history)
        successful_syncs = sum(1 for record in history if record.get("success", False))
        success_rate = (
            round(successful_syncs / total_syncs * 100, 2) if total_syncs > 0 else 0
        )

        last_sync = history[-1] if history else None
        total_records_synced = sum(
            record.get("result", {}).get("success", 0)
            + record.get("result", {}).get("skipped", 0)
            for record in history
        )

        return {
            "total_syncs": total_syncs,
            "success_rate": success_rate,
            "last_sync": last_sync,
            "total_records_synced": total_records_synced,
        }

    def save_uploaded_data(self, uploaded_data: List[Dict]) -> Dict:
        """保存上传的爬取数据"""
        try:
            # 加载现有数据
            existing_data = self.load_crawled_data()
            existing_ids = {item["id"] for item in existing_data}

            # 过滤重复数据
            new_data = []
            duplicate_count = 0

            for item in uploaded_data:
                if item.get("id") not in existing_ids:
                    new_data.append(item)
                else:
                    duplicate_count += 1

            if new_data:
                # 合并数据
                all_data = existing_data + new_data

                # 保存到文件
                os.makedirs(os.path.dirname(self.crawl_data_file), exist_ok=True)
                with open(self.crawl_data_file, "w", encoding="utf-8") as f:
                    json.dump(all_data, f, ensure_ascii=False, indent=2)

                logger.info(
                    f"成功保存 {len(new_data)} 条新数据，跳过 {duplicate_count} 条重复数据"
                )

                return {
                    "success": True,
                    "message": f"成功保存 {len(new_data)} 条新数据",
                    "saved_count": len(new_data),
                    "duplicate_count": duplicate_count,
                    "total_count": len(all_data),
                }
            else:
                return {
                    "success": True,
                    "message": "没有新数据需要保存",
                    "saved_count": 0,
                    "duplicate_count": duplicate_count,
                    "total_count": len(existing_data),
                }

        except Exception as e:
            logger.error(f"保存上传数据失败: {e}")
            return {"success": False, "error": str(e), "message": "保存数据失败"}

    def perform_direct_sync(self, data_list: List[Dict]) -> Dict:
        """一步式数据同步：直接接收数据并同步到数据库"""
        start_time = datetime.now()

        if not data_list:
            return {
                "success": False,
                "message": "没有提供要同步的数据",
                "timestamp": start_time.isoformat(),
            }

        logger.info(f"开始一步式同步 {len(data_list)} 条记录到MySQL数据库")

        try:
            # 创建数据库同步实例
            db_sync = create_database_sync()

            # 连接数据库
            if not db_sync.connect():
                return {
                    "success": False,
                    "message": "数据库连接失败",
                    "timestamp": start_time.isoformat(),
                }

            try:
                # 批量同步数据
                result = db_sync.sync_multiple_items(data_list)

                # 更新同步状态
                sync_status = self.load_sync_status()

                # 只标记真正成功的记录
                for item_id in result.get("success_ids", []):
                    sync_status[item_id] = True

                # 跳过的记录也标记为已同步（因为数据已存在）
                for item_id in result.get("skipped_ids", []):
                    sync_status[item_id] = True

                self.save_sync_status(sync_status)

                # 保存同步历史
                end_time = datetime.now()
                selected_ids = [item["id"] for item in data_list]
                history_record = {
                    "timestamp": start_time.isoformat(),
                    "duration": (end_time - start_time).total_seconds(),
                    "selected_ids": selected_ids,
                    "result": result,
                    "success": result["failed"] == 0,
                    "sync_type": "direct",  # 标记为一步式同步
                }
                self.save_sync_history(history_record)

                result_message = f"一步式数据库同步完成：成功 {result['success']} 条，跳过 {result['skipped']} 条，失败 {result['failed']} 条"
                logger.info(result_message)

                return {
                    "success": True,
                    "message": result_message,
                    "result": result,
                    "timestamp": start_time.isoformat(),
                    "duration": (end_time - start_time).total_seconds(),
                }

            finally:
                db_sync.disconnect()

        except Exception as e:
            error_message = f"一步式数据库同步过程发生错误: {str(e)}"
            logger.error(error_message)
            return {
                "success": False,
                "message": error_message,
                "timestamp": start_time.isoformat(),
            }
