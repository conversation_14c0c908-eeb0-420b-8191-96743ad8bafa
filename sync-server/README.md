# Crawl4AI 同步服务端

## 概述

Crawl4AI同步服务端是一个独立的数据同步服务，提供RESTful API接口，支持：
- 接收爬虫客户端上传的数据
- 执行数据库同步操作
- 自动下载海报图片到本地
- 提供同步状态和统计信息

## 功能特性

- ✅ **数据接收**: 接收客户端上传的爬取数据
- ✅ **数据库同步**: 直接连接MySQL数据库进行数据同步
- ✅ **海报下载**: 自动下载海报图片到本地存储
- ✅ **API接口**: 完整的RESTful API
- ✅ **状态管理**: 同步状态跟踪和历史记录
- ✅ **统计分析**: 详细的同步统计信息

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

编辑 `config.py` 文件，修改数据库连接配置：

```python
DATABASE_CONFIG = {
    'host': 'your_mysql_host',
    'port': 3306,
    'user': 'your_username',
    'password': 'your_password',
    'database': 'your_database'
}
```

### 3. 启动服务

```bash
python server.py
```

服务将在 `http://0.0.0.0:8080` 启动。

## API接口

### 基础信息

- **基础URL**: `http://your_server:8080/api/v1`
- **认证方式**: API Key
- **API密钥**: `crawl4ai_sync_2024`

### 主要端点

#### 1. 健康检查
```bash
GET /health
```

#### 2. API信息
```bash
GET /api/v1/info
```

#### 3. 上传数据
```bash
POST /api/v1/data/upload
Content-Type: application/json
X-API-Key: crawl4ai_sync_2024

{
  "data": [
    {
      "id": "123456",
      "title": "话剧名称",
      "venue": "剧场信息",
      "poster": "海报URL",
      ...
    }
  ]
}
```

#### 4. 获取数据
```bash
GET /api/v1/data
X-API-Key: crawl4ai_sync_2024
```

#### 5. 执行同步
```bash
POST /api/v1/sync
Content-Type: application/json
X-API-Key: crawl4ai_sync_2024

{
  "selected_ids": ["123456", "789012"]
}
```

#### 6. 获取统计
```bash
GET /api/v1/sync/statistics
X-API-Key: crawl4ai_sync_2024
```

## 配置说明

### 服务器配置

```python
SERVER_CONFIG = {
    'host': '0.0.0.0',  # 监听地址
    'port': 8080,       # 监听端口
    'debug': False      # 生产环境建议关闭debug
}
```

### API配置

```python
API_CONFIG = {
    'api_key': 'crawl4ai_sync_2024',  # API密钥
    'rate_limit': {
        'requests_per_minute': 60,
        'requests_per_hour': 1000
    },
    'version': 'v1'
}
```

### 海报下载配置

```python
POSTER_DOWNLOAD_PATH = '/path/to/posters'  # 海报存储路径
DOWNLOAD_CONFIG = {
    'timeout': 30,
    'retry_times': 3,
    'headers': {
        'User-Agent': 'Mozilla/5.0 ...'
    }
}
```

## 部署建议

### 1. 生产环境部署

使用 Gunicorn 部署：

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8080 server:app
```

### 2. 使用 Docker

创建 Dockerfile：

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["python", "server.py"]
```

构建和运行：

```bash
docker build -t crawl4ai-sync-server .
docker run -p 8080:8080 -v /path/to/data:/app/data crawl4ai-sync-server
```

### 3. 使用 Nginx 反向代理

```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 监控和维护

### 1. 日志管理

服务端使用标准的 Python logging 模块，日志级别为 INFO。

### 2. 数据备份

定期备份以下目录：
- `data/` - 数据文件
- `downloads/posters/` - 海报文件

### 3. 性能监控

建议监控以下指标：
- API响应时间
- 数据库连接状态
- 磁盘空间使用
- 内存使用情况

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认网络连通性
   - 验证用户权限

2. **API认证失败**
   - 检查API密钥配置
   - 确认请求头格式

3. **海报下载失败**
   - 检查网络连接
   - 验证存储路径权限
   - 检查URL有效性

### 日志查看

```bash
# 查看实时日志
tail -f /var/log/crawl4ai-sync.log

# 查看错误日志
grep ERROR /var/log/crawl4ai-sync.log
```

## 安全注意事项

1. **API密钥管理**
   - 定期更换API密钥
   - 不要在日志中记录密钥

2. **网络安全**
   - 使用HTTPS协议
   - 配置防火墙规则
   - 限制访问来源

3. **数据安全**
   - 定期备份数据
   - 加密敏感信息
   - 监控异常访问

## 支持和联系

如有问题，请检查：
1. 配置文件是否正确
2. 依赖是否完整安装
3. 数据库连接是否正常
4. 网络是否通畅 