#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库同步模块
直接连接MySQL数据库，将爬取的剧目信息同步到数据库
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse

import pymysql
import requests
from config import DATABASE_CONFIG, DOWNLOAD_CONFIG, POSTER_DOWNLOAD_PATH

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseSync:
    """数据库同步类"""

    def __init__(self, config: Dict[str, str]):
        """
        初始化数据库连接

        Args:
            config: 数据库配置字典，包含host, port, user, password, database
        """
        self.config = config
        self.connection = None

    def connect(self) -> bool:
        """
        连接数据库

        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = pymysql.connect(
                host=self.config["host"],
                port=self.config.get("port", 3306),
                user=self.config["user"],
                password=self.config["password"],
                database=self.config["database"],
                charset="utf8mb4",
                autocommit=False,
            )
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False

    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("数据库连接已断开")

    def repertoire_exists(self, name: str) -> Optional[int]:
        """
        检查剧目是否已存在

        Args:
            name: 剧目名称

        Returns:
            Optional[int]: 如果存在返回剧目ID，否则返回None
        """
        try:
            with self.connection.cursor() as cursor:
                sql = "SELECT id FROM t_repertoire WHERE name = %s AND deleted = 1"
                cursor.execute(sql, (name,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"检查剧目是否存在失败: {e}")
            return None

    def theater_exists(self, name: str) -> Optional[int]:
        """
        检查剧场是否已存在（使用模糊查询）

        Args:
            name: 剧场名称

        Returns:
            Optional[int]: 如果存在返回剧场ID，否则返回None
        """
        try:
            with self.connection.cursor() as cursor:
                # 使用模糊查询匹配剧场名称
                sql = "SELECT id FROM t_theater WHERE name LIKE %s AND deleted = 1 LIMIT 1"
                cursor.execute(sql, (f"%{name}%",))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"检查剧场是否存在失败: {e}")
            return None

    def area_exists(self, area_name: str) -> Optional[int]:
        """
        查询地区是否存在

        Args:
            area_name: 地区名称

        Returns:
            Optional[int]: 如果存在返回地区ID，否则返回None
        """
        try:
            with self.connection.cursor() as cursor:
                # 精确匹配地区名称
                sql = "SELECT id FROM t_area WHERE name = %s LIMIT 1"
                cursor.execute(sql, (area_name,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"查询地区失败: {e}")
            return None

    def parse_venue_info(self, venue: str) -> Dict[str, str]:
        """
        解析venue信息，提取地区和剧场名称

        Args:
            venue: venue字符串，格式如"北京市  天桥艺术中心-大剧场"

        Returns:
            Dict: 包含area和theater_name的字典
        """
        result = {"area": "", "theater_name": venue.strip() if venue else ""}

        if not venue:
            return result

        try:
            # 清理多余空格，然后以 | 分割
            cleaned_venue = " ".join(venue.strip().split())
            parts = cleaned_venue.split("|")

            if len(parts) >= 2:
                # 第一个词是地区，去除可能的"市"、"省"后缀
                area = parts[0]
                if area.endswith("市") or area.endswith("省"):
                    area = area[:-1]
                result["area"] = area
                result["theater_name"] = " ".join(parts[1:])

                # 然后以 - 分割，第一个词是剧场名称
                theater_name = result["theater_name"]
                theater_name_parts = theater_name.split("-")
                if len(theater_name_parts) > 1:
                    result["theater_name"] = theater_name_parts[0]

            elif len(parts) == 1:
                # 如果只有一个词，可能是剧场名称，地区为空
                result["theater_name"] = parts[0]

            logger.info(
                f"解析venue: {venue} -> 地区: {result['area']}, 剧场: {result['theater_name']}"
            )

        except Exception as e:
            logger.error(f"解析venue信息失败: {e}")

        return result

    def insert_theater(self, name: str, area_id: Optional[int] = None) -> Optional[int]:
        """
        插入新剧场

        Args:
            name: 剧场名称
            area_id: 地区ID（可选）

        Returns:
            Optional[int]: 插入成功返回剧场ID，否则返回None
        """
        try:
            with self.connection.cursor() as cursor:
                # 截取短标题（最多50个字符）
                short_name = name[:50] if len(name) > 50 else name

                # 插入SQL（包含地区信息）
                sql = """
                INSERT INTO t_theater (
                    name, short_name, city_id, good_rating_rate, focus_number, 
                    merchant_id, deleted, recommend, audit, audit_flag, 
                    status, create_by, create_time, update_by, update_time
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s
                )
                """

                now = datetime.now()
                values = (
                    name,  # name
                    short_name,  # short_name
                    area_id or 0,  # city_id (地区ID)
                    0.00,  # good_rating_rate (默认值)
                    0,  # focus_number (默认值)
                    0,  # merchant_id (默认值)
                    1,  # deleted (1表示未删除)
                    0,  # recommend (1表示推荐)
                    2,  # audit (2表示审核通过)
                    0,  # audit_flag (1表示审核通过)
                    1,  # status (1表示正常)
                    "crawler_system",  # create_by
                    now,  # create_time
                    "crawler_system",  # update_by
                    now,  # update_time
                )

                cursor.execute(sql, values)
                theater_id = cursor.lastrowid

                logger.info(
                    f"成功插入剧场: {name} (ID: {theater_id}, 地区ID: {area_id})"
                )
                return theater_id

        except Exception as e:
            logger.error(f"插入剧场失败: {e}")
            return None

    def insert_repertoire(self, data: Dict) -> Optional[int]:
        """
        插入新剧目

        Args:
            data: 剧目数据

        Returns:
            Optional[int]: 插入成功返回剧目ID，否则返回None
        """
        try:
            with self.connection.cursor() as cursor:
                # 准备插入数据
                title = data.get("title", "").strip()
                if not title:
                    logger.warning("剧目标题为空，跳过插入")
                    return None

                # 获取短剧名，优先使用shortName字段，否则从标题截取
                short_name = data.get("shortName", "").strip()
                if not short_name:
                    # 如果没有shortName字段，从标题截取（最多50个字符）
                    short_name = title[:50] if len(title) > 50 else title
                else:
                    # 确保短剧名长度不超过50个字符
                    short_name = short_name[:50] if len(short_name) > 50 else short_name

                # 处理简介
                introduction = ""
                if data.get("show_description"):
                    if isinstance(data["show_description"], dict):
                        # 如果是字典，提取文本内容
                        intro_parts = []
                        for key, value in data["show_description"].items():
                            if isinstance(value, str) and value.strip():
                                intro_parts.append(f"{key}: {value}")
                        introduction = "\n".join(intro_parts)
                    elif isinstance(data["show_description"], str):
                        introduction = data["show_description"]

                # 限制简介长度（数据库字段是text类型，但为了安全起见限制在2000字符）
                if len(introduction) > 2000:
                    introduction = introduction[:2000] + "..."

                # 处理封面图片
                cover_picture = data.get("poster", "")
                if not cover_picture and data.get("images"):
                    # 如果没有poster但有images，使用第一张图片
                    images = data["images"]
                    if isinstance(images, list) and images:
                        cover_picture = images[0]

                # 下载海报到本地
                local_poster_path = None
                if cover_picture:
                    item_id = data.get("id", "unknown")
                    local_poster_path = download_poster_image(cover_picture, item_id)
                    if local_poster_path:
                        # 生成数据库中保存的路径格式：/profile/crawler/年月日/文件名
                        current_date = datetime.now()
                        date_folder = current_date.strftime("%Y/%m/%d")
                        filename = os.path.basename(local_poster_path)
                        cover_picture = f"/profile/crawler/{date_folder}/{filename}"
                        logger.info(
                            f"海报已下载到本地: {local_poster_path}, 数据库路径: {cover_picture}"
                        )

                # 插入SQL（去除pictures字段）
                sql = """
                INSERT INTO t_repertoire (
                    name, short_name, cover_picture, introduction,
                    rating, recommend, good_rating_rate, focus_number, 
                    temporary_flag, audit, audit_flag, deleted, status,
                    create_by, create_time, update_by, update_time
                ) VALUES (
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s
                )
                """

                now = datetime.now()
                values = (
                    title,  # name
                    short_name,  # short_name
                    cover_picture,  # cover_picture
                    introduction,  # introduction
                    0.00,  # rating (默认值)
                    0,  # recommend (1表示推荐)
                    0.00,  # good_rating_rate (默认值)
                    0,  # focus_number (默认值)
                    0,  # temporary_flag (默认值)
                    2,  # audit (2表示审核通过)
                    1,  # audit_flag (1表示审核通过)
                    1,  # deleted (1表示未删除)
                    1,  # status (1表示正常)
                    "crawler_system",  # create_by
                    now,  # create_time
                    "crawler_system",  # update_by
                    now,  # update_time
                )

                cursor.execute(sql, values)
                repertoire_id = cursor.lastrowid

                logger.info(f"成功插入剧目: {title} (ID: {repertoire_id})")
                return repertoire_id

        except Exception as e:
            logger.error(f"插入剧目失败: {e}")
            return None

    def insert_repertoire_labels(self, repertoire_id: int, data: Dict) -> bool:
        """
        插入剧目标签

        Args:
            repertoire_id: 剧目ID
            data: 剧目数据

        Returns:
            bool: 插入是否成功
        """
        try:
            with self.connection.cursor() as cursor:
                # 提取标签
                labels = []

                # 处理时间段信息作为标签
                if data.get("time_period"):
                    labels.append(f"时间:{data['time_period']}")

                # 从演出描述中提取标签
                if data.get("show_description") and isinstance(
                    data["show_description"], dict
                ):
                    for key, value in data["show_description"].items():
                        if (
                            isinstance(value, str) and len(value) < 50
                        ):  # 只有短文本才作为标签
                            labels.append(f"{key}:{value}")

                # 限制标签数量
                labels = labels[:10]  # 最多10个标签

                # 插入标签
                now = datetime.now()
                for label in labels:
                    if label.strip():
                        sql = """
                        INSERT INTO t_repertoire_label (
                            merchant_id, name, repertoire_id, create_time
                        ) VALUES (
                            %s, %s, %s, %s
                        )
                        """
                        cursor.execute(sql, (0, label.strip(), repertoire_id, now))

                logger.info(f"成功插入 {len(labels)} 个标签")
                return True

        except Exception as e:
            logger.error(f"插入标签失败: {e}")
            return False

    def parse_sessions(self, sessions_data) -> List[Dict]:
        """
        解析sessions字段信息

        Args:
            sessions_data: sessions数据，可能是列表或字符串

        Returns:
            List[Dict]: 解析后的时间信息列表
        """
        sessions = []
        if not sessions_data:
            return sessions

        try:
            import re

            # 如果是列表，直接处理
            if isinstance(sessions_data, list):
                for session_item in sessions_data:
                    if isinstance(session_item, str):
                        # 解析字符串格式的时间
                        sessions.extend(self._parse_session_string(session_item))
                    elif isinstance(session_item, dict):
                        # 处理新的对象格式：{session_time: '2025-11-01 周六 14:30', status: '可购买', raw_text: '...'}
                        if "session_time" in session_item:
                            # 解析time字段中的时间信息
                            parsed_sessions = self._parse_session_string(
                                session_item["session_time"]
                            )
                            if parsed_sessions:
                                # 如果解析成功，使用解析结果
                                sessions.extend(parsed_sessions)
                            else:
                                # 如果解析失败，使用raw_text或原始数据
                                fallback_text = session_item.get(
                                    "raw_text", session_item.get("date_display", "")
                                )
                                if fallback_text:
                                    sessions.extend(
                                        self._parse_session_string(fallback_text)
                                    )
                        else:
                            # 兼容旧格式的字典
                            sessions.append(session_item)
            elif isinstance(sessions_data, str):
                # 如果是字符串，解析时间信息
                sessions.extend(self._parse_session_string(sessions_data))

        except Exception as e:
            logger.error(f"解析sessions失败: {e}")

        return sessions

    def _parse_session_string(self, session_str: str) -> List[Dict]:
        """
        解析单个session字符串

        Args:
            session_str: session字符串，如"2025-07-11 星期五 19:30"

        Returns:
            List[Dict]: 解析后的时间信息列表
        """
        sessions = []
        try:
            import re

            # 匹配完整的日期时间格式：YYYY-MM-DD 星期X/周X HH:MM
            datetime_pattern = r"(\d{4})-(\d{1,2})-(\d{1,2})\s+(?:星期|周)[一二三四五六日天]\s+(\d{1,2}):(\d{2})"
            datetime_match = re.search(datetime_pattern, session_str)

            if datetime_match:
                year, month, day, hour, minute = datetime_match.groups()
                try:
                    start_time = datetime(
                        int(year), int(month), int(day), int(hour), int(minute)
                    )
                    # 假设演出时长2小时
                    end_time = datetime(
                        int(year), int(month), int(day), int(hour) + 2, int(minute)
                    )

                    sessions.append({"start_time": start_time, "end_time": end_time})
                    return sessions
                except ValueError:
                    logger.warning(f"无效的日期时间格式: {datetime_match.groups()}")

            # 匹配简单日期格式：YYYY.MM.DD或YYYY-MM-DD
            date_pattern = r"(\d{4})[-.](\d{1,2})[-.](\d{1,2})"
            matches = re.findall(date_pattern, session_str)

            if matches:
                for match in matches:
                    year, month, day = match
                    try:
                        # 构造日期时间
                        start_time = datetime(
                            int(year), int(month), int(day), 19, 30
                        )  # 默认19:30开始
                        end_time = datetime(
                            int(year), int(month), int(day), 21, 30
                        )  # 默认21:30结束

                        sessions.append(
                            {"start_time": start_time, "end_time": end_time}
                        )
                    except ValueError:
                        logger.warning(f"无效的日期格式: {match}")
                        continue

        except Exception as e:
            logger.error(f"解析session字符串失败: {e}")

        return sessions

    def insert_repertoire_info(
        self, repertoire_id: int, theater_id: int, data: Dict
    ) -> bool:
        """
        插入剧目场次信息

        Args:
            repertoire_id: 剧目ID
            theater_id: 剧场ID
            data: 剧目数据

        Returns:
            bool: 插入是否成功
        """
        try:
            with self.connection.cursor() as cursor:
                # 插入剧目场次信息主表
                sql = """
                INSERT INTO t_repertoire_info (
                    initiate_merchant_id, release_merchant_id, prov_id, theater_id, 
                    repertoire_id, temporary_flag, theater_pass, repertoire_pass, 
                    sort, status, audit, create_by, create_time, update_by, update_time
                ) VALUES (
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s
                )
                """

                now = datetime.now()
                values = (
                    0,  # initiate_merchant_id
                    0,  # release_merchant_id
                    0,  # prov_id (默认值)
                    theater_id,  # theater_id
                    repertoire_id,  # repertoire_id
                    0,  # temporary_flag
                    1,  # theater_pass (1表示通过)
                    1,  # repertoire_pass (1表示通过)
                    0,  # sort
                    1,  # status (1表示已确认)
                    2,  # audit (2表示审核通过)
                    "crawler_system",  # create_by
                    now,  # create_time
                    "crawler_system",  # update_by
                    now,  # update_time
                )

                cursor.execute(sql, values)
                repertoire_info_id = cursor.lastrowid

                # 解析sessions信息并插入详细场次
                sessions = self.parse_sessions(data.get("sessions", []))

                for session in sessions:
                    sql_detail = """
                    INSERT INTO t_repertoire_info_detail (
                        repertoire_info_id, theater_id, repertoire_id, 
                        start_time, end_time, create_by, create_time, 
                        update_by, update_time
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    """

                    values_detail = (
                        repertoire_info_id,  # repertoire_info_id
                        theater_id,  # theater_id
                        repertoire_id,  # repertoire_id
                        session["start_time"],  # start_time
                        session["end_time"],  # end_time
                        "crawler_system",  # create_by
                        now,  # create_time
                        "crawler_system",  # update_by
                        now,  # update_time
                    )

                    cursor.execute(sql_detail, values_detail)

                logger.info(f"成功插入剧目场次信息: {len(sessions)} 个场次")
                return True

        except Exception as e:
            logger.error(f"插入剧目场次信息失败: {e}")
            return False

    def sync_single_item(self, data: Dict) -> Tuple[bool, str]:
        """
        同步单个剧目

        Args:
            data: 剧目数据

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        if not self.connection:
            return False, "数据库未连接"

        try:
            # 开始事务
            self.connection.begin()

            title = data.get("title", "").strip()
            if not title:
                return False, "剧目标题为空"

            # 检查是否已存在
            existing_id = self.repertoire_exists(title)
            if existing_id:
                self.connection.rollback()
                return False, f"剧目已存在 (ID: {existing_id})"

            # 处理场馆信息，获取theater_id
            theater_id = None
            if data.get("venue"):
                venue_info = self.parse_venue_info(data["venue"])
                theater_name = venue_info["theater_name"]
                area_name = venue_info["area"]

                # 查询地区ID
                area_id = None
                if area_name:
                    area_id = self.area_exists(area_name)
                    if not area_id:
                        logger.warning(f"未找到地区: {area_name}")

                # 查询或创建剧场
                if theater_name:
                    theater_id = self.theater_exists(theater_name)
                    if not theater_id:
                        theater_id = self.insert_theater(theater_name, area_id)

            # 插入剧目
            repertoire_id = self.insert_repertoire(data)
            if not repertoire_id:
                self.connection.rollback()
                return False, "插入剧目失败"

            # 插入标签
            if not self.insert_repertoire_labels(repertoire_id, data):
                self.connection.rollback()
                return False, "插入标签失败"

            # 插入场次信息（如果有剧场信息和sessions数据）
            if theater_id and data.get("sessions"):
                if not self.insert_repertoire_info(repertoire_id, theater_id, data):
                    self.connection.rollback()
                    return False, "插入场次信息失败"

            # 提交事务
            self.connection.commit()
            return True, f"成功同步剧目 (ID: {repertoire_id})"

        except Exception as e:
            self.connection.rollback()
            logger.error(f"同步剧目失败: {e}")
            return False, f"同步失败: {str(e)}"

    def sync_multiple_items(self, data_list: List[Dict]) -> Dict:
        """
        批量同步剧目

        Args:
            data_list: 剧目数据列表

        Returns:
            Dict: 同步结果统计，包含成功的ID列表
        """
        result = {
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "total": len(data_list),
            "success_ids": [],  # 新增：成功同步的ID列表
            "failed_ids": [],  # 新增：失败的ID列表
            "skipped_ids": [],  # 新增：跳过的ID列表
        }

        for i, data in enumerate(data_list):
            try:
                title = data.get("title", f"第{i+1}个剧目")
                item_id = data.get("id", f"unknown_{i}")
                logger.info(f"正在同步第 {i+1}/{len(data_list)} 个剧目: {title}")

                success, message = self.sync_single_item(data)
                if success:
                    result["success"] += 1
                    result["success_ids"].append(item_id)
                    logger.info(f"✅ {message}")
                else:
                    if "已存在" in message:
                        result["skipped"] += 1
                        result["skipped_ids"].append(item_id)
                        logger.info(f"⏭️  {message}")
                    else:
                        result["failed"] += 1
                        result["failed_ids"].append(item_id)
                        logger.error(f"❌ {message}")

            except Exception as e:
                result["failed"] += 1
                result["failed_ids"].append(data.get("id", f"unknown_{i}"))
                logger.error(f"❌ 同步第 {i+1} 个剧目失败: {e}")

        return result


def download_poster_image(poster_url: str, item_id: str) -> Optional[str]:
    """
    下载海报图片到本地

    Args:
        poster_url: 海报图片URL
        item_id: 项目ID，用于生成文件名

    Returns:
        Optional[str]: 本地文件路径，下载失败返回None
    """
    if not poster_url or not poster_url.startswith("http"):
        return None

    try:
        # 解析URL获取文件扩展名
        parsed_url = urlparse(poster_url)
        file_extension = os.path.splitext(parsed_url.path)[1]
        if not file_extension:
            file_extension = ".jpg"  # 默认扩展名

        # 生成年月日子文件夹路径
        current_date = datetime.now()
        date_folder = current_date.strftime("%Y/%m/%d")

        # 创建完整的本地目录路径
        local_dir = os.path.join(POSTER_DOWNLOAD_PATH, date_folder)
        os.makedirs(local_dir, exist_ok=True)

        # 生成本地文件名
        filename = f"{item_id}_poster{file_extension}"
        local_path = os.path.join(local_dir, filename)

        # 如果文件已存在，直接返回路径
        if os.path.exists(local_path):
            logger.info(f"海报文件已存在: {local_path}")
            return local_path

        # 下载图片
        for attempt in range(DOWNLOAD_CONFIG["retry_times"]):
            try:
                response = requests.get(
                    poster_url,
                    headers=DOWNLOAD_CONFIG["headers"],
                    timeout=DOWNLOAD_CONFIG["timeout"],
                    stream=True,
                )
                response.raise_for_status()

                # 保存文件
                with open(local_path, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                logger.info(f"成功下载海报: {poster_url} -> {local_path}")
                return local_path

            except Exception as e:
                logger.warning(
                    f"下载海报失败 (尝试 {attempt + 1}/{DOWNLOAD_CONFIG['retry_times']}): {e}"
                )
                if attempt == DOWNLOAD_CONFIG["retry_times"] - 1:
                    logger.error(f"海报下载最终失败: {poster_url}")
                    return None

    except Exception as e:
        logger.error(f"下载海报时发生错误: {e}")
        return None


def create_database_sync() -> DatabaseSync:
    """
    创建数据库同步实例

    Returns:
        DatabaseSync: 数据库同步实例
    """
    return DatabaseSync(DATABASE_CONFIG)


def test_sync():
    """测试同步功能"""
    # 创建同步实例
    sync = create_database_sync()

    # 连接数据库
    if not sync.connect():
        print("数据库连接失败")
        return

    try:
        # 加载测试数据
        # 尝试多个可能的数据文件路径
        data_paths = [
            "crawl_data/details_data.json",
            "../data/details_data.json",
            "../crawler-client/crawl_data/details_data.json",
        ]

        data = None
        for path in data_paths:
            try:
                with open(path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                print(f"成功加载数据文件: {path}")
                break
            except FileNotFoundError:
                continue

        if data is None:
            print("未找到测试数据文件")
            return

        print(f"加载了 {len(data)} 条数据")

        # 同步前几条数据进行测试
        test_data = data[:3]  # 只测试前3条
        result = sync.sync_multiple_items(test_data)

        print(f"同步结果: {result}")

    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        sync.disconnect()


if __name__ == "__main__":
    test_sync()
