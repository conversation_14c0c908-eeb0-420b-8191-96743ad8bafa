# Git相关
.git
.gitignore
README.md
*.md

# Python相关
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# 虚拟环境
venv/
ENV/
env/
.venv/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定
crawl_data/
downloads/
data/
logs/
*.tar.gz
.env

# 保留必要的目录结构
!crawler-client/crawl_data/
!sync-server/downloads/
!sync-server/data/ 