version: "3.8"

services:
  crawl4ai-app:
    build:
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
    platform: linux/amd64
    container_name: crawl4ai-app
    ports:
      - "5000:5000" # 爬虫客户端端口
      - "5001:5001" # 同步服务器端口
    volumes:
      - ./crawler-client/crawl_data:/app/crawler-client/crawl_data
      - ./sync-server/downloads:/app/sync-server/downloads
      - ./sync-server/data:/app/sync-server/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - DISPLAY=:99
      # 数据库配置（可以通过环境变量覆盖）
      - DB_HOST=127.0.0.1
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=VYw^%jk6U^
      - DB_DATABASE=digital_collection
    restart: unless-stopped
    healthcheck:
      test:
        [
          "C<PERSON>",
          "python",
          "-c",
          "import requests; requests.get('http://localhost:5000/health', timeout=5) and requests.get('http://localhost:5001/health', timeout=5)",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    security_opt:
      - seccomp:unconfined
    cap_add:
      - SYS_ADMIN

  # 如果需要数据库服务，可以添加
  # db:
  #   image: mysql:8.0
  #   container_name: crawl4ai-db
  #   environment:
  #     MYSQL_ROOT_PASSWORD: VYw^%jk6U^
  #     MYSQL_DATABASE: digital_collection
  #     MYSQL_USER: crawl4ai
  #     MYSQL_PASSWORD: crawl4ai_pass
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./sql:/docker-entrypoint-initdb.d
  #   restart: unless-stopped
# volumes:
#   mysql_data:
