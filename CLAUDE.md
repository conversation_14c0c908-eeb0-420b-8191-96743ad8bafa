# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## System Overview

Crawl4AI is a distributed web scraping and data synchronization system designed for collecting performance/theater data from Damai.cn. The system uses a client-server architecture with two main components:

- **crawler-client/**: Flask-based web scraping client with browser automation
- **sync-server/**: RESTful API service for database synchronization and poster downloads

## Architecture

```
┌─────────────────┐     HTTP API     ┌─────────────────┐
│  Crawler Client │◄────────────────►│  Sync Server    │
│  (Port 5000)    │                  │  (Port 5001)    │
│                 │                  │                 │
│ • Web UI        │                  • RESTful API     │
│ • Playwright    │                  • MySQL Sync      │
│ • Local Storage │                  • Poster Download │
└─────────────────┘                  └─────────────────┘
                                           │
                                           ▼
                                  ┌─────────────────┐
                                  │   MySQL DB      │
                                  │   (5 tables)    │
                                  └─────────────────┘
```

## Quick Commands

### Development Setup
```bash
# Install dependencies
cd crawler-client && pip install -r requirements.txt
cd sync-server && pip install -r requirements.txt

# Start services locally
python sync-server/server.py    # Port 5001
python crawler-client/client.py   # Port 5000

# Docker setup
docker-compose up -d           # Uses env variables from .env
```

### Docker Operations
```bash
# Build unified image
./build.sh

# Run with docker-compose
docker-compose up -d

# Health check
curl http://localhost:5000/status
curl http://localhost:5001/health
```

### Configuration Files

Key configuration files to modify for different environments:
- `crawler-client/config.py` - Scraping settings, server URLs
- `sync-server/config.py` - Database connection, poster paths
- `.env` - Environment variables for Docker deployment

### Core Components

#### Crawler Client (`crawler-client/`)
- **client.py:20-30** - Flask web server with async crawling
- **deep_crawler.py** - Playwright-based scraping engine
- **sync_client.py** - HTTP client for sync server communication
- **templates/index.html** - React-like web interface

#### Sync Server (`sync-server/`)
- **server.py:30-50** - Flask REST API with authentication
- **sync_service.py** - Database synchronization logic
- **database_sync.py** - MySQL operations for 5 tables
- Poster download to configurable directory

### API Endpoints

#### Sync Server (Port 5001)
- `GET /health` - Health check
- `POST /api/v1/data/upload` - Upload scraped data
- `POST /api/v1/sync` - Sync to MySQL database
- `GET /api/v1/sync/statistics` - Sync stats

#### Crawler Client (Port 5000)
- `GET /status` - Crawling status
- `POST /start_crawl` - Start scraping
- `POST /sync/upload_data` - Upload to sync server

### Environment Variables

Required for Docker deployment:
```bash
MYSQL_HOST=127.0.0.1
MYSQL_PASSWORD=your_password
POSTER_DOWNLOAD_PATH=/path/to/posters
API_KEY=crawl4ai_sync_2024
```

### Data Flow

1. **Scraping**: Damai.cn → Crawler Client (local JSON storage)
2. **Upload**: Client → Sync Server (via HTTP API)
3. **Sync**: Server → MySQL database (5 related tables)
4. **Posters**: Downloaded to `POSTER_DOWNLOAD_PATH` directory

### Key Dependencies

**Client**: Flask, Playwright, BeautifulSoup4, Requests
**Server**: Flask, PyMySQL, Requests
**Both**: Use API key authentication (`crawl4ai_sync_2024`)