#!/bin/bash

# 设置错误时退出
set -e

echo "📦 导出 crawl4AI amd64 架构 Docker 镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查镜像是否存在
if ! docker images | grep -q "crawl4ai-app"; then
    echo "❌ 镜像不存在，请先构建镜像"
    echo "📝 运行: ./build_amd64.sh"
    exit 1
fi

# 生成时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
IMAGE_NAME="crawl4ai-app"
EXPORT_FILE="crawl4ai-app_amd64_${TIMESTAMP}.tar.gz"

echo "🔍 检查镜像架构..."

# 使用更准确的方法检测镜像架构
ARCH_INFO=$(docker inspect $IMAGE_NAME:latest --format='{{.Architecture}}')
if [ -z "$ARCH_INFO" ]; then
    # 备用方法：从JSON输出中提取架构信息
    ARCH_INFO=$(docker inspect $IMAGE_NAME:latest | jq -r '.[0].Architecture' 2>/dev/null || echo "unknown")
fi

echo "📋 镜像架构: $ARCH_INFO"

# 检查架构是否为 amd64
if [ "$ARCH_INFO" = "amd64" ] || [ "$ARCH_INFO" = "x86_64" ]; then
    echo "✅ 镜像架构正确: $ARCH_INFO"
    ARCH_TYPE="amd64"
elif [ "$ARCH_INFO" = "arm64" ] || [ "$ARCH_INFO" = "aarch64" ]; then
    echo "⚠️  警告: 镜像架构是 ARM64 ($ARCH_INFO)，不是 amd64"
    ARCH_TYPE="arm64"
    read -p "是否继续导出? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 导出已取消"
        exit 1
    fi
else
    echo "⚠️  警告: 无法确定镜像架构 ($ARCH_INFO)"
    read -p "是否继续导出? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 导出已取消"
        exit 1
    fi
    ARCH_TYPE="unknown"
fi

# 根据实际架构调整文件名
if [ "$ARCH_TYPE" != "amd64" ]; then
    EXPORT_FILE="crawl4ai-app_${ARCH_TYPE}_${TIMESTAMP}.tar.gz"
    echo "📝 调整导出文件名: $EXPORT_FILE"
fi

echo "📦 开始导出镜像..."
docker save $IMAGE_NAME:latest | gzip > $EXPORT_FILE

# 检查文件大小
FILE_SIZE=$(du -h $EXPORT_FILE | cut -f1)
echo "✅ 镜像导出完成: $EXPORT_FILE (大小: $FILE_SIZE)"

# 显示导出文件信息
echo "📋 导出文件信息:"
ls -lh $EXPORT_FILE

# 显示镜像详细信息
echo "🔍 镜像详细信息:"
echo "   镜像名称: $IMAGE_NAME:latest"
echo "   架构: $ARCH_INFO"
echo "   操作系统: $(docker inspect $IMAGE_NAME:latest --format='{{.Os}}')"
echo "   创建时间: $(docker inspect $IMAGE_NAME:latest --format='{{.Created}}')"

echo ""
echo "📝 导入镜像命令:"
echo "   gunzip -c $EXPORT_FILE | docker load"
echo ""
echo "📝 或者直接导入:"
echo "   docker load < $EXPORT_FILE"
echo ""
echo "📝 验证导入:"
echo "   docker images | grep crawl4ai" 